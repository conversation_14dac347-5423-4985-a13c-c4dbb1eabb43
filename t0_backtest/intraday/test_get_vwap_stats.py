from tracemalloc import start
import pandas as pd
import numpy as np

def calculate_weighted_vwap(algo_parentorder, tca_basic_analysis, start_date, end_date, target_account_id):
    """
    计算指定日期和账户ID的加权平均VWAP
    
    参数:
    - algo_parentorder: 父订单DataFrame
    - tca_basic_analysis: TCA分析结果DataFrame
    - target_date: 目标日期(YYYYMMDD格式)
    - target_account_id: 目标账户ID
    
    返回:
    - 加权平均VWAP
    """
    # 1. 筛选父订单
    # filtered_orders = algo_parentorder[
    #     (pd.to_datetime(algo_parentorder['date']) >= pd.to_datetime(start_date)) &
    #     (pd.to_datetime(algo_parentorder['date']) <= pd.to_datetime(end_date)) &
    #     (algo_parentorder['account_id'] == target_account_id)
    # ].copy()
    
    # if filtered_orders.empty:
    #     raise ValueError(f"没有找到日期为{start_date}到{end_date}的账户ID为{target_account_id}的父订单")
    
    # 2. 获取订单ID列表
    order_ids = algo_parentorder['id'].tolist()
    
    # # 3. 筛选TCA结果
    # matched_tca = tca_basic_analysis[
    #     tca_basic_analysis['id'].isin(order_ids)
    # ].copy()
    
    # if matched_tca.empty:
    #     raise ValueError(f"在TCA分析结果中找不到匹配的订单ID")
    
    # 4. 合并数据
    merged_data = pd.merge(
        tca_basic_analysis,
        algo_parentorder[['id', 'filled_quantity', 'filled_price']],
        on='id',
        how='left'
    )
    
    # 5. 检查数据完整性
    if merged_data['filled_quantity'].isnull().any() or merged_data['filled_price'].isnull().any():
        missing_ids = merged_data[
            merged_data['filled_quantity'].isnull() | 
            merged_data['filled_price'].isnull()
        ]['id'].tolist()
        # drop
        merged_data = merged_data[~merged_data['id'].isin(missing_ids)]
        # raise ValueError(f"以下订单ID缺少filled_quantity或filled_price数据: {missing_ids}")
    
    # 6. 计算成交金额
    merged_data['executed_notional'] = merged_data['filled_price'] * merged_data['filled_quantity']
    
    # 7. 计算加权平均VWAP
    weighted_vwap = np.average(
        merged_data['vwap'],
        weights=merged_data['executed_notional'] / merged_data['executed_notional'].sum(),
    )
    
    return weighted_vwap

# 使用示例(实际数据)



from utils.mysql import get_zs_trading_data_db_connection

engine = get_zs_trading_data_db_connection()


start_date = '********'
end_date = '********'
target_account_id = '10573688_2105_8382'

sql = f"SELECT * FROM algo_parentorder WHERE date >= '{start_date}' AND date <= '{end_date}' AND account_id = '{target_account_id}'"

algo_parentorder = pd.read_sql_query(sql, engine)
print(algo_parentorder.head(5))
# all_ids = algo_parentorder['id'].tolist()


sql = f"SELECT * FROM tca_basic_analysis WHERE id IN (SELECT id FROM algo_parentorder WHERE date >= '{start_date}' AND date <= '{end_date}' AND account_id = '{target_account_id}')"
tca_basic_analysis = pd.read_sql_query(sql, engine)
print(tca_basic_analysis.head(5))


result = calculate_weighted_vwap(algo_parentorder, tca_basic_analysis, start_date, end_date, target_account_id)
print(f"加权平均VWAP: {result}")


# algo_parentorder = pd.read_csv('algo_parentorder.csv')  # 或从数据库读取
# tca_basic_analysis = pd.read_csv('tca_basic_analysis.csv')  # 或从数据库读取
# target_date = ********
# target_account_id = '9225553_1102_1102'
# result = calculate_weighted_vwap(algo_parentorder, tca_basic_analysis, target_date, target_account_id)
# print(f"加权平均VWAP: {result}")
