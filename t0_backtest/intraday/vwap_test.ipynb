{"cells": [{"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from utils import mysql\n", "import numpy as np\n", "import pandas as pd\n", "from utils import mysql\n", "from data import data_reader\n", "import datetime\n", "from joblib import Parallel, delayed, Memory"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["def fmt_td(delta):\n", "    total_seconds = int(delta.total_seconds())\n", "    # 分解为小时、分钟、秒\n", "    hours, remainder = divmod(total_seconds, 3600)\n", "    minutes, seconds = divmod(remainder, 60)\n", "    formatted_time = f\"{hours:02d}:{minutes:02d}:{seconds:02d}\"\n", "    return formatted_time\n", "def stats(datas):\n", "    d={}\n", "    d['executed_value']=datas['executed_notional'].sum()\n", "    d['no_of_porders']=len(datas)\n", "    d['po_amt_mean']=(datas['vwap_full']*datas['quantity']).mean()\n", "    d['po_amt_med']=(datas['vwap_full']*datas['quantity']).median()\n", "    d['vwap']=datas['vwap_full'].mean()\n", "    d['vwap_cost']=-np.average(datas['vwap_cost'],weights=datas['executed_notional']/d['executed_value'])\n", "    d['vwap_costf50']=-np.average(datas['vwap_cost_f50'],weights=datas['executed_notional']/d['executed_value'])\n", "    d['vwap_costl50']=-np.average(datas['vwap_cost_l50'],weights=datas['executed_notional']/d['executed_value'])\n", "    return d\n", "\n", "def show_by_name(datas,groups,stats_func):\n", "    l=[]\n", "    for gn in groups:\n", "        if gn=='total':\n", "            r=stats_func(datas)\n", "            r['group']=gn\n", "            l.append(r)\n", "        else: \n", "            for n,g in datas.groupby(gn):\n", "                r=stats_func(g)\n", "                r['group']=n\n", "                l.append(r)\n", "    return pd.DataFrame(l)\n", "\n", "def analysis(po):\n", "    tks=pd.read_feather(r\"/data/shared-data/public/snapshot/syms/{}/{}\".format(po['date'],po['symbol']))\n", "    d={}\n", "    starttime=po['st']\n", "    endtime=po['et']\n", "    middletime=po['mt']\n", "    # print(starttime,endtime)\n", "    interval_tks=tks[(tks['time']>=starttime)&(tks['time']<=endtime)]\n", "    vwap_full=(interval_tks.iloc[-1]['Turnover']-interval_tks.iloc[0]['Turnover'])/(interval_tks.iloc[-1]['TradVolume']-interval_tks.iloc[0]['TradVolume'])\n", "    interval_tks=tks[(tks['time']>=starttime)&(tks['time']<=middletime)]\n", "    first50_vwap=(interval_tks.iloc[-1]['Turnover']-interval_tks.iloc[0]['Turnover'])/(interval_tks.iloc[-1]['TradVolume']-interval_tks.iloc[0]['TradVolume'])\n", "    interval_tks=tks[(tks['time']>=middletime)&(tks['time']<=endtime)]\n", "    last50_vwap=(interval_tks.iloc[-1]['Turnover']-interval_tks.iloc[0]['Turnover'])/(interval_tks.iloc[-1]['TradVolume']-interval_tks.iloc[0]['TradVolume'])\n", "    side=-1 if po['operation']==1 else 1\n", "    d['id']=po['id']\n", "    d['vwap_full']=vwap_full\n", "    d['first50_vwap']=first50_vwap\n", "    d['last50_vwap']=last50_vwap\n", "    d['side']=side\n", "    d.update(po)\n", "    return d\n", "def calc_cost(avg_px,bm_px,side):\n", "    return  side*(avg_px/bm_px -1 )*10000"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["# 可以考虑缓存已加载的数据\n", "from functools import lru_cache\n", "\n", "memory = Memory(location='./cachedir', verbose=0)\n", "\n", "@memory.cache\n", "def load_tks(date, symbol):\n", "    return pd.read_feather(f\"/data/shared-data/public/snapshot/syms/{date}/{symbol}\")\n", "\n", "\n", "# 更精确的VWAP计算函数\n", "def calculate_vwap(df):\n", "    df = df.sort_values('time')\n", "    if len(df) < 2:\n", "        return 0\n", "    try:\n", "        vwap = (df['Turnover'].iloc[-1] - df['Turnover'].iloc[0]) / (df['TradVolume'].iloc[-1] - df['TradVolume'].iloc[0])\n", "    except:\n", "        # print(f\"calculate_vwap error: {df}\")\n", "        print(f\"calculate_vwap error: {df['Turnover'].iloc[-1]} - {df['Turnover'].iloc[0]} = {df['TradVolume'].iloc[-1]} - {df['TradVolume'].iloc[0]}\")\n", "        return 0\n", "    return vwap\n", "    \n", "    # df['cum_turnover'] = df['Turnover'].cumsum()\n", "    # df['cum_volume'] = df['TradVolume'].cumsum()\n", "    # return df['cum_turnover'].iloc[-1] / df['cum_volume'].iloc[-1]\n", "\n", "\n", "def new_analysis(po):\n", "    # 缓存数据加载\n", "    tks = load_tks(po['date'], po['symbol'])\n", "    \n", "    # 确保时间列是Timestamp类型\n", "    tks['time'] = pd.to_datetime(tks['time'])\n", "    \n", "    # 获取时间边界\n", "    starttime, endtime = po['st'], po['et']\n", "    total_duration = (endtime - starttime).total_seconds()\n", "    \n", "    # 计算关键时间点\n", "    t25 = starttime + pd.<PERSON><PERSON><PERSON>(seconds=total_duration * 0.25)\n", "    t50 = starttime + pd.<PERSON><PERSON><PERSON>(seconds=total_duration * 0.5)\n", "    t75 = starttime + pd.<PERSON><PERSON><PERSON>(seconds=total_duration * 0.75)\n", "    \n", "    # 时间过滤 - 获取全时段数据\n", "    interval_tks = tks[(tks['time']>=starttime)&(tks['time']<=endtime)].copy()\n", "    \n", "    # 防御性编程\n", "    if len(interval_tks) < 2:\n", "        return {**po, \n", "                'vwap_0_100': 0, \n", "                'vwap_0_25': 0, \n", "                'vwap_0_50': 0, \n", "                'vwap_25_75': 0,  # 修正变量名\n", "                'vwap_50_100': 0, \n", "                'vwap_75_100': 0, \n", "                'side': -1 if po['operation']==1 else 1}\n", "    \n", "    \n", "    # 计算各时间段VWAP\n", "    # 1. 0-25%时间段 (前25%)\n", "    vwap_0_25 = calculate_vwap(interval_tks[interval_tks['time']<=t25]) if len(interval_tks[interval_tks['time']<=t25]) >= 2 else 0\n", "    \n", "    # 2. 0-50%时间段 (前50%，包含前25%)\n", "    vwap_0_50 = calculate_vwap(interval_tks[interval_tks['time']<=t50]) if len(interval_tks[interval_tks['time']<=t50]) >= 2 else 0\n", "    \n", "    # 3. 25%-75%时间段 (中间50%)\n", "    vwap_25_75 = calculate_vwap(interval_tks[(interval_tks['time']>=t25)&(interval_tks['time']<=t75)]) if len(interval_tks[(interval_tks['time']>=t25)&(interval_tks['time']<=t75)]) >= 2 else 0\n", "    \n", "    # 4. 50%-100%时间段 (后50%，包含后25%)\n", "    vwap_50_100 = calculate_vwap(interval_tks[interval_tks['time']>=t50]) if len(interval_tks[interval_tks['time']>=t50]) >= 2 else 0\n", "    \n", "    # 5. 75%-100%时间段 (后25%)\n", "    vwap_75_100 = calculate_vwap(interval_tks[interval_tks['time']>=t75]) if len(interval_tks[interval_tks['time']>=t75]) >= 2 else 0\n", "    \n", "    # 6. 0-100%全时段\n", "    vwap_0_100 = calculate_vwap(interval_tks)\n", "    \n", "    # 交易方向\n", "    side = -1 if po['operation']==1 else 1\n", "    \n", "    # 返回结果\n", "    return {\n", "        **po,\n", "        'vwap_0_100': vwap_0_100,  # 全时段(0-100%)\n", "        'vwap_0_25': vwap_0_25,    # 前25%\n", "        'vwap_0_50': vwap_0_50,    # 前50%(包含前25%)\n", "        'vwap_25_75': vwap_25_75,  # 中间50%(25%-75%)\n", "        'vwap_50_100': vwap_50_100,# 后50%(包含后25%)\n", "        'vwap_75_100': vwap_75_100,# 后25%\n", "        'side': side\n", "    }"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["select * from algo_parentorder where date>='20250601' and date<='20250630'\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/trade/t0_backtest/intraday/utils/mysql.py:86: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.\n", "  return pd.read_sql(sql,conn)\n"]}], "source": ["sd=\"20250601\"\n", "ed=\"20250630\"\n", "sql=\"select * from algo_parentorder where date>='{}' and date<='{}'\".format(sd,ed)\n", "pos=mysql.query(mysql.get_zs_trading_data_db_connection(),sql)\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["       start_time  end_time\n", "0           94000    101000\n", "1           94000    101000\n", "2           94000    101000\n", "3           94000    101000\n", "4           94000    101000\n", "...           ...       ...\n", "56906       93500    112959\n", "56907       93500    112959\n", "56908       93500    112959\n", "56909       93500    112959\n", "56910       93500    112959\n", "\n", "[56911 rows x 2 columns]\n", "93000\n", "145600\n", "[101000 133000 112959 145500 133400 100000 143700 133700 143600 145600\n", " 142700 134300  93500 110000 135400 133300 111000 133200 113000 101545\n", " 133900 133100]\n", "{'date': ********, 'id': 'guojun_ld_********10000', 'operation': 1, 'create_time': 90800, 'start_time': 94000, 'end_time': 101000, 'symbol': '600048', 'quantity': 100, 'filled_quantity': 100.0, 'filled_price': 8.1, 'account_id': '9225553_1102_1102', 'algo_name': 'vwap', 'firm': 'wuzhi', 'algo_provider': 'zhishu', 'pm': 'xuj', 'broker': 'guotaijunan', 'params': '', 'sys_type': 'kafang_zongxian', 'remark1': '', 'remark2': '', 'remark3': '', 'st': Timestamp('2025-06-03 09:40:00'), 'et': Timestamp('2025-06-03 10:10:00'), 'mt': Timestamp('2025-06-03 09:55:00')}\n"]}], "source": ["print(pos[['start_time','end_time']])\n", "print(pos['start_time'].min())\n", "print(pos['end_time'].max())\n", "print(pos['end_time'].unique())\n", "key = pos.to_dict('records')[0]\n", "print(key)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:41: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:43: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/**********.py:45: RuntimeWarning: invalid value encountered in scalar divide\n"]}], "source": ["pos['st']=pd.to_datetime(pos['date'].astype(str)+pos['start_time'].apply(lambda x:str(x).zfill(6)),format=\"%Y%m%d%H%M%S\")\n", "pos['et']=pd.to_datetime(pos['date'].astype(str)+pos['end_time'].apply(lambda x:str(x).zfill(6)),format=\"%Y%m%d%H%M%S\")\n", "pos['mt']=pos['st']+(pos['et']-pos['st'])/2\n", "result=Parallel(n_jobs=40,max_nbytes=None)(delayed(analysis)(po) for po in pos.to_dict('records'))  \n", "tca_df=pd.DataFrame([_ for _ in result if _ is not None]) \n", "tca_df=tca_df.fillna(0)\n"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>executed_value</th>\n", "      <th>no_of_porders</th>\n", "      <th>po_amt_mean</th>\n", "      <th>po_amt_med</th>\n", "      <th>vwap</th>\n", "      <th>vwap_cost</th>\n", "      <th>vwap_costf50</th>\n", "      <th>vwap_costl50</th>\n", "      <th>group</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>7.332578e+06</td>\n", "      <td>566</td>\n", "      <td>13104.718948</td>\n", "      <td>11495.369433</td>\n", "      <td>14.708624</td>\n", "      <td>-0.610064</td>\n", "      <td>-4.064691</td>\n", "      <td>4.401323</td>\n", "      <td>10573688_2105_8382</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2.965718e+07</td>\n", "      <td>1845</td>\n", "      <td>16875.003372</td>\n", "      <td>17849.221522</td>\n", "      <td>17.934174</td>\n", "      <td>-1.225256</td>\n", "      <td>-0.423646</td>\n", "      <td>0.160470</td>\n", "      <td>28131077_3104_3104</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>5.168732e+07</td>\n", "      <td>8282</td>\n", "      <td>6857.664611</td>\n", "      <td>4087.694968</td>\n", "      <td>12.918278</td>\n", "      <td>-1.110045</td>\n", "      <td>-0.191409</td>\n", "      <td>-0.623048</td>\n", "      <td>2____10313____111____49____072000002135____</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3.532225e+07</td>\n", "      <td>2450</td>\n", "      <td>14589.972905</td>\n", "      <td>8871.195600</td>\n", "      <td>19.997941</td>\n", "      <td>-1.886816</td>\n", "      <td>-0.040278</td>\n", "      <td>1.453981</td>\n", "      <td>2____10336____103361____49____0650032828____</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5.507139e+07</td>\n", "      <td>18858</td>\n", "      <td>2956.731787</td>\n", "      <td>1925.487979</td>\n", "      <td>14.492708</td>\n", "      <td>-3.532913</td>\n", "      <td>-0.997669</td>\n", "      <td>0.841855</td>\n", "      <td>2____10355____10355____49____8883558888____</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>8.051592e+07</td>\n", "      <td>17483</td>\n", "      <td>5401.455496</td>\n", "      <td>3394.623242</td>\n", "      <td>15.959294</td>\n", "      <td>-2.106146</td>\n", "      <td>-1.385759</td>\n", "      <td>1.227498</td>\n", "      <td>9225553_1102_1102</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>1.118025e+08</td>\n", "      <td>7427</td>\n", "      <td>15189.099540</td>\n", "      <td>9209.563104</td>\n", "      <td>15.426676</td>\n", "      <td>4.066957</td>\n", "      <td>0.291477</td>\n", "      <td>0.886188</td>\n", "      <td>clz_zhongtai_109156033251</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   executed_value  no_of_porders   po_amt_mean    po_amt_med       vwap  \\\n", "0    7.332578e+06            566  13104.718948  11495.369433  14.708624   \n", "1    2.965718e+07           1845  16875.003372  17849.221522  17.934174   \n", "2    5.168732e+07           8282   6857.664611   4087.694968  12.918278   \n", "3    3.532225e+07           2450  14589.972905   8871.195600  19.997941   \n", "4    5.507139e+07          18858   2956.731787   1925.487979  14.492708   \n", "5    8.051592e+07          17483   5401.455496   3394.623242  15.959294   \n", "6    1.118025e+08           7427  15189.099540   9209.563104  15.426676   \n", "\n", "   vwap_cost  vwap_costf50  vwap_costl50  \\\n", "0  -0.610064     -4.064691      4.401323   \n", "1  -1.225256     -0.423646      0.160470   \n", "2  -1.110045     -0.191409     -0.623048   \n", "3  -1.886816     -0.040278      1.453981   \n", "4  -3.532913     -0.997669      0.841855   \n", "5  -2.106146     -1.385759      1.227498   \n", "6   4.066957      0.291477      0.886188   \n", "\n", "                                          group  \n", "0                            10573688_2105_8382  \n", "1                            28131077_3104_3104  \n", "2   2____10313____111____49____072000002135____  \n", "3  2____10336____103361____49____0650032828____  \n", "4   2____10355____10355____49____8883558888____  \n", "5                             9225553_1102_1102  \n", "6                     clz_zhongtai_109156033251  "]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["tca_df['vwap_cost']=calc_cost(tca_df['filled_price'],tca_df['vwap_full'],tca_df['side'])\n", "tca_df['vwap_cost_f50']=calc_cost(tca_df['first50_vwap'],tca_df['vwap_full'],tca_df['side'])\n", "tca_df['vwap_cost_l50']=calc_cost(tca_df['last50_vwap'],tca_df['vwap_full'],tca_df['side'])\n", "tca_df['executed_notional']=tca_df['filled_price']*tca_df['filled_quantity']\n", "tca_df=tca_df.fillna(0)\n", "show_by_name(tca_df,['account_id'],stats)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>executed_value</th>\n", "      <th>no_of_porders</th>\n", "      <th>po_amt_mean</th>\n", "      <th>po_amt_med</th>\n", "      <th>vwap_cost</th>\n", "      <th>vwap_costf50</th>\n", "      <th>vwap_costl50</th>\n", "      <th>group</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>7.332578e+06</td>\n", "      <td>566</td>\n", "      <td>13104.718948</td>\n", "      <td>11495.369433</td>\n", "      <td>-0.610064</td>\n", "      <td>-4.064691</td>\n", "      <td>4.401323</td>\n", "      <td>10573688_2105_8382</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2.965718e+07</td>\n", "      <td>1845</td>\n", "      <td>16875.003372</td>\n", "      <td>17849.221522</td>\n", "      <td>-1.225256</td>\n", "      <td>-0.423646</td>\n", "      <td>0.160470</td>\n", "      <td>28131077_3104_3104</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>5.168732e+07</td>\n", "      <td>8282</td>\n", "      <td>6857.664611</td>\n", "      <td>4087.694968</td>\n", "      <td>-1.110045</td>\n", "      <td>-0.191409</td>\n", "      <td>-0.623048</td>\n", "      <td>2____10313____111____49____072000002135____</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3.532225e+07</td>\n", "      <td>2450</td>\n", "      <td>14589.972905</td>\n", "      <td>8871.195600</td>\n", "      <td>-1.886816</td>\n", "      <td>-0.040278</td>\n", "      <td>1.453981</td>\n", "      <td>2____10336____103361____49____0650032828____</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5.507139e+07</td>\n", "      <td>18858</td>\n", "      <td>2956.731787</td>\n", "      <td>1925.487979</td>\n", "      <td>-3.532913</td>\n", "      <td>-0.997669</td>\n", "      <td>0.841855</td>\n", "      <td>2____10355____10355____49____8883558888____</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>8.051592e+07</td>\n", "      <td>17483</td>\n", "      <td>5401.455496</td>\n", "      <td>3394.623242</td>\n", "      <td>-2.106146</td>\n", "      <td>-1.385759</td>\n", "      <td>1.227498</td>\n", "      <td>9225553_1102_1102</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>1.118025e+08</td>\n", "      <td>7427</td>\n", "      <td>15189.099540</td>\n", "      <td>9209.563104</td>\n", "      <td>4.066957</td>\n", "      <td>0.291477</td>\n", "      <td>0.886188</td>\n", "      <td>clz_zhongtai_109156033251</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   executed_value  no_of_porders   po_amt_mean    po_amt_med  vwap_cost  \\\n", "0    7.332578e+06            566  13104.718948  11495.369433  -0.610064   \n", "1    2.965718e+07           1845  16875.003372  17849.221522  -1.225256   \n", "2    5.168732e+07           8282   6857.664611   4087.694968  -1.110045   \n", "3    3.532225e+07           2450  14589.972905   8871.195600  -1.886816   \n", "4    5.507139e+07          18858   2956.731787   1925.487979  -3.532913   \n", "5    8.051592e+07          17483   5401.455496   3394.623242  -2.106146   \n", "6    1.118025e+08           7427  15189.099540   9209.563104   4.066957   \n", "\n", "   vwap_costf50  vwap_costl50                                         group  \n", "0     -4.064691      4.401323                            10573688_2105_8382  \n", "1     -0.423646      0.160470                            28131077_3104_3104  \n", "2     -0.191409     -0.623048   2____10313____111____49____072000002135____  \n", "3     -0.040278      1.453981  2____10336____103361____49____0650032828____  \n", "4     -0.997669      0.841855   2____10355____10355____49____8883558888____  \n", "5     -1.385759      1.227498                             9225553_1102_1102  \n", "6      0.291477      0.886188                     clz_zhongtai_109156033251  "]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": []}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n", "/tmp/ipykernel_1922901/2467163052.py:17: RuntimeWarning: invalid value encountered in scalar divide\n"]}], "source": ["pos['st']=pd.to_datetime(pos['date'].astype(str)+pos['start_time'].apply(lambda x:str(x).zfill(6)),format=\"%Y%m%d%H%M%S\")\n", "pos['et']=pd.to_datetime(pos['date'].astype(str)+pos['end_time'].apply(lambda x:str(x).zfill(6)),format=\"%Y%m%d%H%M%S\")\n", "# pos['mt']=pos['st']+(pos['et']-pos['st'])/2\n", "result_2=Parallel(n_jobs=40,max_nbytes=None)(delayed(new_analysis)(po) for po in pos.to_dict('records'))  \n", "tca_df_2=pd.DataFrame([_ for _ in result_2 if _ is not None]) \n", "tca_df_2=tca_df_2.fillna(0)\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>executed_value</th>\n", "      <th>no_of_porders</th>\n", "      <th>po_amt_mean</th>\n", "      <th>po_amt_med</th>\n", "      <th>vwap</th>\n", "      <th>vwap_cost</th>\n", "      <th>vwap_cost_0_50</th>\n", "      <th>vwap_cost_50_100</th>\n", "      <th>vwap_cost_0_25</th>\n", "      <th>vwap_cost_25_75</th>\n", "      <th>vwap_cost_75_100</th>\n", "      <th>group</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>7.332578e+06</td>\n", "      <td>566</td>\n", "      <td>13104.718948</td>\n", "      <td>11495.369433</td>\n", "      <td>14.708624</td>\n", "      <td>-0.610064</td>\n", "      <td>-4.064691</td>\n", "      <td>4.401323</td>\n", "      <td>-3.012549</td>\n", "      <td>-2.414148</td>\n", "      <td>9.997749</td>\n", "      <td>10573688_2105_8382</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2.965718e+07</td>\n", "      <td>1845</td>\n", "      <td>16875.003372</td>\n", "      <td>17849.221522</td>\n", "      <td>17.934174</td>\n", "      <td>-1.225256</td>\n", "      <td>-0.423646</td>\n", "      <td>0.160470</td>\n", "      <td>0.262445</td>\n", "      <td>-0.346111</td>\n", "      <td>0.759878</td>\n", "      <td>28131077_3104_3104</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>5.168732e+07</td>\n", "      <td>8282</td>\n", "      <td>6857.664611</td>\n", "      <td>4087.694968</td>\n", "      <td>12.918278</td>\n", "      <td>-1.110045</td>\n", "      <td>-0.191409</td>\n", "      <td>-0.623048</td>\n", "      <td>-0.135810</td>\n", "      <td>-0.452993</td>\n", "      <td>-0.795314</td>\n", "      <td>2____10313____111____49____072000002135____</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3.532225e+07</td>\n", "      <td>2450</td>\n", "      <td>14589.972905</td>\n", "      <td>8871.195600</td>\n", "      <td>19.997941</td>\n", "      <td>-1.886816</td>\n", "      <td>-0.040278</td>\n", "      <td>1.453981</td>\n", "      <td>-0.328862</td>\n", "      <td>0.886003</td>\n", "      <td>1.894210</td>\n", "      <td>2____10336____103361____49____0650032828____</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5.507139e+07</td>\n", "      <td>18858</td>\n", "      <td>2956.731787</td>\n", "      <td>1925.487979</td>\n", "      <td>14.492708</td>\n", "      <td>-3.532913</td>\n", "      <td>-0.997669</td>\n", "      <td>0.841855</td>\n", "      <td>-2.206111</td>\n", "      <td>0.418686</td>\n", "      <td>0.566454</td>\n", "      <td>2____10355____10355____49____8883558888____</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>8.051592e+07</td>\n", "      <td>17483</td>\n", "      <td>5401.455496</td>\n", "      <td>3394.623242</td>\n", "      <td>15.959294</td>\n", "      <td>-2.106146</td>\n", "      <td>-1.385759</td>\n", "      <td>1.227498</td>\n", "      <td>-2.559812</td>\n", "      <td>0.312300</td>\n", "      <td>1.383842</td>\n", "      <td>9225553_1102_1102</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>1.118025e+08</td>\n", "      <td>7427</td>\n", "      <td>15189.099540</td>\n", "      <td>9209.563104</td>\n", "      <td>15.426676</td>\n", "      <td>4.066957</td>\n", "      <td>0.291477</td>\n", "      <td>0.886188</td>\n", "      <td>2.011369</td>\n", "      <td>-0.698466</td>\n", "      <td>1.774219</td>\n", "      <td>clz_zhongtai_109156033251</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>1.991597e+08</td>\n", "      <td>31319</td>\n", "      <td>6925.080302</td>\n", "      <td>3469.335120</td>\n", "      <td>15.632640</td>\n", "      <td>5.291039</td>\n", "      <td>1.679605</td>\n", "      <td>1.161418</td>\n", "      <td>5.250292</td>\n", "      <td>-0.375314</td>\n", "      <td>2.450271</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>1.722294e+08</td>\n", "      <td>25592</td>\n", "      <td>6924.304893</td>\n", "      <td>3019.620386</td>\n", "      <td>14.641016</td>\n", "      <td>-6.549635</td>\n", "      <td>-3.031571</td>\n", "      <td>0.401511</td>\n", "      <td>-6.858946</td>\n", "      <td>0.143849</td>\n", "      <td>-0.147311</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   executed_value  no_of_porders   po_amt_mean    po_amt_med       vwap  \\\n", "0    7.332578e+06            566  13104.718948  11495.369433  14.708624   \n", "1    2.965718e+07           1845  16875.003372  17849.221522  17.934174   \n", "2    5.168732e+07           8282   6857.664611   4087.694968  12.918278   \n", "3    3.532225e+07           2450  14589.972905   8871.195600  19.997941   \n", "4    5.507139e+07          18858   2956.731787   1925.487979  14.492708   \n", "5    8.051592e+07          17483   5401.455496   3394.623242  15.959294   \n", "6    1.118025e+08           7427  15189.099540   9209.563104  15.426676   \n", "7    1.991597e+08          31319   6925.080302   3469.335120  15.632640   \n", "8    1.722294e+08          25592   6924.304893   3019.620386  14.641016   \n", "\n", "   vwap_cost  vwap_cost_0_50  vwap_cost_50_100  vwap_cost_0_25  \\\n", "0  -0.610064       -4.064691          4.401323       -3.012549   \n", "1  -1.225256       -0.423646          0.160470        0.262445   \n", "2  -1.110045       -0.191409         -0.623048       -0.135810   \n", "3  -1.886816       -0.040278          1.453981       -0.328862   \n", "4  -3.532913       -0.997669          0.841855       -2.206111   \n", "5  -2.106146       -1.385759          1.227498       -2.559812   \n", "6   4.066957        0.291477          0.886188        2.011369   \n", "7   5.291039        1.679605          1.161418        5.250292   \n", "8  -6.549635       -3.031571          0.401511       -6.858946   \n", "\n", "   vwap_cost_25_75  vwap_cost_75_100  \\\n", "0        -2.414148          9.997749   \n", "1        -0.346111          0.759878   \n", "2        -0.452993         -0.795314   \n", "3         0.886003          1.894210   \n", "4         0.418686          0.566454   \n", "5         0.312300          1.383842   \n", "6        -0.698466          1.774219   \n", "7        -0.375314          2.450271   \n", "8         0.143849         -0.147311   \n", "\n", "                                          group  \n", "0                            10573688_2105_8382  \n", "1                            28131077_3104_3104  \n", "2   2____10313____111____49____072000002135____  \n", "3  2____10336____103361____49____0650032828____  \n", "4   2____10355____10355____49____8883558888____  \n", "5                             9225553_1102_1102  \n", "6                     clz_zhongtai_109156033251  \n", "7                                             0  \n", "8                                             1  "]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["def calculate_period_performance(df):\n", "    \"\"\"\n", "    计算6个时段的绩效指标\n", "    \"\"\"\n", "    # 确保所有VWAP列都存在\n", "    required_columns = ['vwap_0_100', 'vwap_0_25', 'vwap_0_50', 'vwap_25_75', \n", "                       'vwap_50_100', 'vwap_75_100', 'filled_price', 'side', 'filled_quantity']\n", "    \n", "    missing_cols = [col for col in required_columns if col not in df.columns]\n", "    if missing_cols:\n", "        raise ValueError(f\"缺少必要的列: {missing_cols}\")\n", "    \n", "    # 计算各时段执行成本\n", "    df['vwap_cost_0_100'] = calc_cost(df['filled_price'], df['vwap_0_100'], df['side'])\n", "    df['vwap_cost_0_25'] = calc_cost(df['vwap_0_25'], df['vwap_0_100'], df['side'])\n", "    df['vwap_cost_0_50'] = calc_cost(df['vwap_0_50'], df['vwap_0_100'], df['side'])\n", "    df['vwap_cost_25_75'] = calc_cost(df['vwap_25_75'], df['vwap_0_100'], df['side'])\n", "    df['vwap_cost_50_100'] = calc_cost(df['vwap_50_100'], df['vwap_0_100'], df['side'])\n", "    df['vwap_cost_75_100'] = calc_cost(df['vwap_75_100'], df['vwap_0_100'], df['side'])\n", "    \n", "    # 计算执行名义金额\n", "    df['executed_notional'] = df['filled_price'] * df['filled_quantity']\n", "    \n", "    # 填充NA值\n", "    df = df.fillna(0)\n", "    \n", "    return df\n", "\n", "# 汇总统计\n", "def new_stats(datas):\n", "    d = {}\n", "    d['executed_value'] = datas['executed_notional'].sum()\n", "    d['no_of_porders'] = len(datas)\n", "    d['po_amt_mean'] = (datas['vwap_0_100'] * datas['quantity']).mean()\n", "    d['po_amt_med'] = (datas['vwap_0_100'] * datas['quantity']).median()\n", "    d['vwap'] = datas['vwap_0_100'].mean()\n", "    d['vwap_cost'] = -np.average(\n", "        datas['vwap_cost_0_100'], \n", "        weights=datas['executed_notional']/d['executed_value']\n", "    )\n", "    d['vwap_cost_0_50'] = -np.average(\n", "        datas['vwap_cost_0_50'],\n", "        weights=datas['executed_notional']/d['executed_value']\n", "    )\n", "    d['vwap_cost_50_100'] = -np.average(\n", "        datas['vwap_cost_50_100'], \n", "        weights=datas['executed_notional']/d['executed_value']\n", "    )\n", "    # 新增6个时段的统计\n", "    d['vwap_cost_0_25'] = -np.average(\n", "        datas['vwap_cost_0_25'], \n", "        weights=datas['executed_notional']/d['executed_value']\n", "    )\n", "    d['vwap_cost_25_75'] = -np.average(\n", "        datas['vwap_cost_25_75'], \n", "        weights=datas['executed_notional']/d['executed_value']\n", "    )\n", "    d['vwap_cost_75_100'] = -np.average(\n", "        datas['vwap_cost_75_100'], \n", "        weights=datas['executed_notional']/d['executed_value']\n", "    )\n", "    return d\n", "\n", "\n", "# 计算各时段绩效\n", "tca_df_2 = calculate_period_performance(tca_df_2)\n", "\n", "\n", "\n", "# 输出结果\n", "performance_result = new_stats(tca_df_2)\n", "# print(performance_result)\n", "\n", "# 按账户ID分组展示绩效统计\n", "show_by_name(tca_df_2, ['account_id', 'operation'], new_stats)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}