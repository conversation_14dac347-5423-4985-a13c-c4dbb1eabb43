# TCA交易成本分析系统

## 📊 系统简介

TCA交易成本分析系统是一个基于Streamlit的交互式分析平台，用于分析算法交易的执行效果和成本。系统提供多维度的数据分析、交互式图表展示和PDF报告导出功能。

## 🚀 快速开始

### 1. 环境要求
- Python 3.7+
- MySQL数据库连接
- 相关Python依赖包（见requirements.txt）

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 启动应用
```bash
# 方式1：使用启动脚本
./run_tca_app.sh

# 方式2：直接运行
streamlit run tca_streamlit_app.py
```

### 4. 访问应用
打开浏览器访问：http://localhost:8501

## 🔍 功能特性

### 数据筛选
- **时间范围选择**：支持自定义日期范围
- **账户筛选**：可多选特定账户ID
- **公司筛选**：按公司维度筛选
- **券商筛选**：按券商维度筛选
- **算法提供商筛选**：按算法提供商筛选

### 分析维度
- **指数成分分组**：按股票所属指数分析
- **价格分组**：按股价水平分组分析
- **时间间隔分组**：按订单执行时长分析
- **订单规模分组**：按订单大小分析
- **换手率分组**：按股票换手率分析

### 关键指标
- **VWAP成本**：相对于VWAP的交易成本（bp）
- **到达成本**：相对于到达价格的交易成本（bp）
- **完成率**：订单执行完成度
- **撤单率**：订单撤销比例
- **胜率**：盈利订单占比

### 图表类型
- **柱状图**：各分组指标分布
- **饼图**：分组占比分析
- **时间序列图**：趋势分析
- **统计表格**：详细数据展示

### 报告导出
- **PDF报告**：包含完整分析结果的PDF文档
- **数据下载**：支持表格数据导出

## 📋 使用流程

1. **设置筛选条件**
   - 在左侧边栏选择时间范围
   - 选择需要分析的账户、公司、券商等
   
2. **开始分析**
   - 点击"开始分析"按钮
   - 系统自动加载和处理数据
   
3. **查看结果**
   - 基本统计信息概览
   - 多维度分组分析
   - 交互式图表展示
   
4. **导出报告**
   - 点击"导出PDF报告"
   - 下载完整分析报告

## 🛠️ 技术架构

- **前端框架**：Streamlit
- **数据处理**：Pandas, NumPy
- **图表库**：Plotly
- **PDF生成**：ReportLab
- **数据库**：MySQL

## 📊 数据源

系统从以下数据表获取数据：
- `algo_parentorder`：主订单信息
- `tca_basic_analysis`：基础TCA分析数据
- `tca_other_analysis`：其他TCA分析数据
- `tca_open_analysis`：开盘TCA分析数据

## 🔧 配置说明

### 数据库配置
确保在`utils/mysql.py`中正确配置数据库连接信息。

### 字段映射
系统内置中英文字段映射，可在`FIELD_NAME_MAPPING`字典中自定义。

## 📈 分析示例

### 指数成分分析
- 比较沪深300、中证1000等不同指数成分股的交易表现
- 分析各指数的VWAP成本、完成率等指标

### 价格分组分析
- 按股价水平（极低价、低价、中价、高价、极高价）分析
- 发现不同价格区间的交易特征

### 时间间隔分析
- 按订单执行时长分组
- 分析执行时间对交易成本的影响

## 🚨 注意事项

1. **数据权限**：确保有相应数据库表的访问权限
2. **网络连接**：需要稳定的数据库连接
3. **数据量**：大数据量分析可能需要较长时间
4. **浏览器兼容性**：推荐使用Chrome或Firefox

## 📞 技术支持

如遇问题，请检查：
1. 数据库连接是否正常
2. Python依赖是否完整安装
3. 数据权限是否充足
4. 筛选条件是否合理

## 🔄 更新日志

- v1.0.0：初始版本，包含基础分析功能
- 支持多维度分组分析
- 集成Plotly交互式图表
- 添加PDF导出功能
