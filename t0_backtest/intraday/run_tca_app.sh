#!/bin/bash

# TCA Streamlit应用启动脚本

echo "🚀 启动TCA交易成本分析系统..."

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3未安装，请先安装Python3"
    exit 1
fi

# 检查Streamlit是否安装
if ! python3 -c "import streamlit" &> /dev/null; then
    echo "📦 正在安装依赖包..."
    pip3 install -r requirements.txt
fi

# 设置环境变量
export PYTHONPATH="${PYTHONPATH}:../../"

# 启动Streamlit应用
echo "🌐 启动Streamlit应用..."
echo "📊 TCA分析系统将在浏览器中打开"
echo "🔗 访问地址: http://localhost:8501"
echo ""
echo "按 Ctrl+C 停止应用"
echo ""

streamlit run tca_streamlit_app.py --server.port 8501 --server.address 0.0.0.0
