{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import pandas as pd\n", "from utils import mysql\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "import warnings\n", "pd.set_option('display.max_columns', None)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def fmt_td(delta):\n", "    total_seconds = int(delta.total_seconds())\n", "    # 分解为小时、分钟、秒\n", "    hours, remainder = divmod(total_seconds, 3600)\n", "    minutes, seconds = divmod(remainder, 60)\n", "    formatted_time = f\"{hours:02d}:{minutes:02d}:{seconds:02d}\"\n", "    return formatted_time\n", "\n", "def show_by_name(datas,groups,stats_func):\n", "    l=[]\n", "    for gn in groups:\n", "        if gn=='total':\n", "            r=stats_func(datas)\n", "            r['group']=gn\n", "            l.append(r)\n", "        else: \n", "            for n,g in datas.groupby(gn):\n", "                r=stats_func(g)\n", "                r['group']=n\n", "                l.append(r)\n", "    return pd.DataFrame(l)\n", "\n", "def show_by_multi_name(datas,grp,stats_func):\n", "    l=[]   \n", "    for names,g in datas.groupby(grp):\n", "        r=stats_func(g)\n", "        for i,n in enumerate(names):\n", "            r['group_{}'.format(i)]=n\n", "        l.append(r)\n", "    return pd.DataFrame(l)\n", "\n", "def stats(datas):\n", "    d={}\n", "    d['executed_value']=datas['executed_notional'].sum()\n", "    d['no_of_porders']=len(datas)\n", "    d['po_amt_mean']=(datas['arrival_px']*datas['quantity']).mean()\n", "    d['po_amt_med']=(datas['arrival_px']*datas['quantity']).median()\n", "    d['po_lots_mean']=(datas['quantity']/datas['lot_size']).mean()\n", "    d['po_lots_med']=(datas['quantity']/datas['lot_size']).median()\n", "    d['arrival_cost']=-np.average(datas['arrival_cost'],weights=datas['executed_notional']/d['executed_value'])\n", "    d['open_cost']=-np.average(datas['open_cost'],weights=datas['executed_notional']/d['executed_value'])\n", "    d['close_cost']=-np.average(datas['close_cost'],weights=datas['executed_notional']/d['executed_value'])\n", "    d['vwap_cost']=-np.average(datas['vwap_cost'],weights=datas['executed_notional']/d['executed_value'])\n", "    d['vwap_cost2']=-np.average(datas['vwap_cost2'],weights=datas['executed_notional2']/datas['executed_notional2'].sum())\n", "    d['avg_cpt_rate']=datas['cplt_rate'].mean()\n", "    d['wavg_cpt_rate']=np.average(datas['cplt_rate'],weights=(datas['arrival_px']*datas['quantity'])/(datas['arrival_px']*datas['quantity']).sum())\n", "    if datas['cancel_slices'].sum()==0:\n", "        d['cxl_rate']=0\n", "    else:\n", "        d['cxl_rate']=datas['cancel_slices'].sum()/(datas['total_slices']-datas['error_slices']).sum()\n", "    d['target_exe_time']=fmt_td((datas['target_exe_time']).mean())\n", "    d['avg_order_size']=datas['avg_order_size'].mean()\n", "    d['avg_order_value']=(datas['executed_notional']/(datas['total_slices']-datas['error_slices'])).mean()\n", "    # d['avg_filled_time']=datas['avg_filled_time'].mean()\n", "    # d['avg_cancel_time']=datas['avg_cancel_time'].mean()\n", "    d['win_r']=datas['win_rate'].mean()\n", "    d['num_order']=len(datas)\n", "    return d\n", "\n", "def read_datas(sd,ed,algo_provider=None,firm=None):\n", "    sql=\"select * from algo_parentorder where date>='{}' and date<='{}'\".format(sd,ed)\n", "    sql+= \" and algo_provider='{}'\".format(algo_provider) if algo_provider is not None else \"\"\n", "    sql+= \" and firm='{}'\".format(firm) if firm is not None else \"\"\n", "    pos=mysql.query(mysql.get_zs_trading_data_db_connection(),sql)\n", "    tca1=mysql.query(mysql.get_zs_trading_data_db_connection(),\"select * from tca_basic_analysis where id in {}\".format(tuple(pos['id'].values)))\n", "    tca2=mysql.query(mysql.get_zs_trading_data_db_connection(),\"select * from tca_other_analysis where id in {}\".format(tuple(pos['id'].values)))\n", "    tca3=mysql.query(mysql.get_zs_trading_data_db_connection(),\"select * from tca_open_analysis where id in {}\".format(tuple(pos['id'].values)))\n", "    datas=pos.merge(tca1,on='id',how='left').merge(tca2,on='id',how='left').merge(tca3,on='id',how='left')\n", "    datas=datas.fillna(0)\n", "    datas['date']=datas['date'].astype(str)\n", "    datas['executed_notional']=datas['filled_quantity']*datas['filled_price']\n", "    datas['start_time']=pd.to_datetime(datas['date']+datas['start_time'].apply(lambda x:str(x).zfill(6)))\n", "    datas['end_time']=pd.to_datetime(datas['date']+datas['end_time'].apply(lambda x:str(x).zfill(6)))\n", "    datas['target_exe_time']=datas['end_time']-datas['start_time']\n", "    datas['filled_price2']=np.where((datas['filled_quantity']<datas['quantity'])&(datas['oppo_px']>0),((datas['filled_quantity']*datas['filled_price'])+datas['oppo_px']*(datas['quantity']-datas['filled_quantity']))/(datas['quantity']),datas['filled_price'])\n", "    datas['executed_notional2']=datas['filled_price2']*datas['quantity']\n", "    datas['vwap_cost2']=(datas['side']*(datas['filled_price2']/datas['vwap'] -1)*10000).fillna(0)\n", "    datas['open_cost']=(datas['side']*(datas['filled_price']/datas['open_px'] -1)*10000).fillna(0)\n", "    datas['close_cost']=(datas['side']*(datas['filled_price']/datas['close_px'] -1)*10000).fillna(0)\n", "    datas['lot_size']=datas['symbol'].apply(lambda x:200 if x[:3]=='688' else 100)\n", "    datas=datas[datas['side']!=0]\n", "    return datas\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "# notebook_dir = Path.cwd() \n", "\n", "# print(str(notebook_dir.parent))\n", "# sys.path.insert(0, str(notebook_dir.parent))\n", "# from utils import mysql\n", "# sys.path.remove(str(notebook_dir.parent))\n", "\n", "\n", "# print(str(notebook_dir.parent.parent))\n", "# sys.path.insert(0, str(notebook_dir.parent.parent))\n", "# from misc.ssh_conn import ftp_clent_zx_zhongtai\n", "# from misc.Readstockfile import read_remote_file, write_file\n", "# sys.path.remove(str(notebook_dir.parent.parent))\n", "\n", "\n", "cur_dir = Path.cwd()\n", "p_dir = os.path.join(cur_dir, '../../')\n", "sys.path.insert(0, p_dir)\n", "from data_utils.trading_calendar import Calendar\n", "from misc.Readstockfile import update_xlsx_putdf\n", "from misc import tools\n", "\n", "sys.path.remove(p_dir)\n", "\n", "warnings.filterwarnings('ignore') "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "\n", "\n", "def datas_add_cons_group(datas):\n", "    # sql = \"select * from algo_parentorder where date>='{}' and date<='{}'\".format(datas['date'].min(), datas['date'].max())\n", "    # algo_parentorder = pd.read_sql(sql, mysql.get_zs_trading_data_db_connection())\n", "    \n", "    # datas = datas.merge(algo_parentorder, on='id', how='left')\n", "    l = []\n", "    dates = datas['date'].unique()\n", "    for date in dates:\n", "        cons_tag = tools.get_cons_tag_series(date)\n", "        cons = cons_tag.to_frame('index_name').reset_index()\n", "        cons['ticker'] = cons['ticker'].astype(str).str.z<PERSON>(6)\n", "        cons.rename(columns={'ticker': 'symbol'}, inplace=True)\n", "        cons['date'] = date\n", "        l.append(cons)\n", "    if len(l) > 0:\n", "        cons = pd.concat(l, axis=0, ignore_index=True)\n", "    else:\n", "        cons = pd.DataFrame(columns=['symbol', 'date', 'index_name'])\n", "        \n", "    datas = datas.merge(cons, on=['symbol', 'date'], how='left')\n", "    datas['index_name'].fillna('OTHER', inplace=True)\n", "    \n", "    return datas\n", "\n", "def datas_add_some_group(datas):\n", "    import datetime\n", "    # price_groups = {\n", "    #     'extreme_low': (0.0, 2.00),\n", "    #     'low': (2.00, 4.00),\n", "    #     'mid': (4.00, 10.00),\n", "    #     'high': (10.00, 20.00),\n", "    #     'extreme_high': (20.00, 9999.00),\n", "    # }\n", "    # datas['price_group'] = np.nan\n", "    # for group_name, group_range in price_groups.items():\n", "    #     datas.loc[(datas['close_px'] >= group_range[0]) & (datas['close_px'] < group_range[1]), 'price_group'] = group_name\n", "        \n", "    bins =   [0.0, 2.00, 4.00, 10.00, 20.00, 9999.00]\n", "    labels = ['extreme_low', 'low', 'mid', 'high', 'extreme_high']\n", "    datas['price_group'] = pd.cut(datas['close_px'], bins=bins, labels=labels, right=False)\n", "    \n", "    # add interval column\n", "    bins = [datetime.timedelta(minutes=i) for i in [0, 15, 30, 60, 240]]\n", "    labels = ['0<t<=15m', '15m<t<=30m', '30m<t<=1h', '1h<t<=4h']\n", "    datas['interval_group'] = pd.cut(datas['date'], bins=bins, labels=labels, right=True)\n", "    \n", "    bins = [0, 500, 1000, 5000, 9999999]\n", "    labels = ['100-500股', '500-1000股', '1000-5000股', '5000股以上']\n", "    datas['po_volume_group'] = pd.cut(datas['quantity'], bins=bins, labels=labels, right=True)\n", "    \n", "    return datas\n", "\n", "\n", "def datas_add_turnover_rate_group(datas):\n", "    l = []\n", "    dates = datas['date'].unique()\n", "    for date in dates:\n", "        tag = tools.get_turnover_rate_by_date(date)\n", "\n", "        tag['ticker'] = tag['ticker'].astype(str).str.zfill(6)\n", "        tag.rename(columns={'ticker': 'symbol'}, inplace=True)\n", "        tag['date'] = date\n", "        l.append(tag)\n", "    if len(l) > 0:\n", "        tag = pd.concat(l, axis=0, ignore_index=True)\n", "    else:\n", "        tag = pd.DataFrame(columns=['symbol', 'date', 'turnover_rate'])\n", "        \n", "    datas = datas.merge(tag, on=['symbol', 'date'], how='left')\n", "    bins = [0, 0.01, 0.10, 0.20, 1.00]\n", "    labels = ['tr_low', 'tr_mid', 'tr_high', 'tr_extreme_high']\n", "    datas['turnover_group'] = pd.cut(datas['turnover_rate'], bins=bins, labels=labels, right=False)\n", "    return datas\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(datas[datas['quantity']< 100])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["datas = read_datas('20250615', '20250623')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["datas = datas_add_cons_group(datas)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["datas = datas_add_some_group(datas)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["datas = datas_add_turnover_rate_group(datas)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(datas.head(10))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total = show_by_name(datas, ['total', 'turnover_group'], stats)\n", "print(total)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total_orders = len(datas)\n", "total_value = datas['executed_notional'].sum()\n", "avg_vwap_bps = np.average(datas['vwap_cost'], weights=datas['executed_notional'] / total_value)\n", "total_vwap_revenue = (datas['vwap_cost'] * datas['executed_notional'] / 10000).sum()\n", "\n", "print(f'Total Parent Orders: {total_orders}')\n", "print(f'Total Transaction Value (yuan): {total_value:,.2f}')\n", "print(f'Average VWAP Performance (bps): {avg_vwap_bps:.2f}')\n", "print(f'Total VWAP Revenue (yuan): {total_vwap_revenue:,.2f}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["daily_summary = datas.groupby('date').agg(\n", "    total_value=('executed_notional', 'sum'),\n", "    avg_vwap=('vwap_cost', lambda x: np.average(x, weights=datas.loc[x.index, 'executed_notional'])),\n", "    order_count=('id', 'count')\n", ").reset_index()\n", "\n", "fig, ax1 = plt.subplots(figsize=(12, 6))\n", "\n", "ax1.bar(daily_summary['date'], daily_summary['total_value'] / 10000, color='red', label='Transaction Volume (10k yuan)')\n", "ax1.set_xlabel('Date')\n", "ax1.set_ylabel('Transaction Volume (10k yuan)', color='red')\n", "ax1.tick_params(axis='y', labelcolor='red')\n", "\n", "ax2 = ax1.twinx()\n", "ax2.plot(daily_summary['date'], daily_summary['avg_vwap'], color='gold', marker='o', label='VWAP Performance (bps)')\n", "ax2.set_ylabel('VWAP Performance (bps)', color='gold')\n", "ax2.tick_params(axis='y', labelcolor='gold')\n", "\n", "plt.title('Daily Transaction Performance')\n", "fig.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["by_day = show_by_name(datas=datas, groups=['date'], stats_func=stats)\n", "print(by_day.head())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(daily_summary.head(5))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 时间序列分组统计和图表绘制"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 中文字段映射字典\n", "field_name_mapping = {\n", "    # 分组字段\n", "    'index_name': '指数成分',\n", "    'price_group': '价格分组',\n", "    'interval_group': '时间间隔',\n", "    'po_volume_group': '订单规模',\n", "    'turnover_group': '换手率分组',\n", "    'date': '日期',\n", "    \n", "    # 统计指标\n", "    'executed_value': '成交金额',\n", "    'no_of_porders': '订单数量',\n", "    'po_amt_mean': '平均订单金额',\n", "    'po_amt_med': '订单金额中位数',\n", "    'po_lots_mean': '平均订单手数',\n", "    'po_lots_med': '订单手数中位数',\n", "    'arrival_cost': '到达成本',\n", "    'open_cost': '开盘成本',\n", "    'close_cost': '收盘成本',\n", "    'vwap_cost': 'VWAP成本',\n", "    'vwap_cost2': 'VWAP成本2',\n", "    'avg_cpt_rate': '平均完成率',\n", "    'wavg_cpt_rate': '加权平均完成率',\n", "    'cxl_rate': '撤单率',\n", "    'target_exe_time': '目标执行时间',\n", "    'avg_order_size': '平均订单规模',\n", "    'avg_order_value': '平均订单价值',\n", "    'win_r': '胜率',\n", "    'num_order': '订单总数',\n", "    \n", "    # 分组值映射\n", "    'ZZ1000': '中证1000',\n", "    'ZZ2000': '中证2000', \n", "    'HS300': '沪深300',\n", "    'OTHER': '其他',\n", "    'extreme_low': '极低价',\n", "    'low': '低价',\n", "    'mid': '中价',\n", "    'high': '高价',\n", "    'extreme_high': '极高价',\n", "    '0<t<=15m': '15分钟内',\n", "    '15m<t<=30m': '15-30分钟',\n", "    '30m<t<=1h': '30分钟-1小时',\n", "    '1h<t<=4h': '1-4小时',\n", "    '100-500股': '100-500股',\n", "    '500-1000股': '500-1000股',\n", "    '1000-5000股': '1000-5000股',\n", "    '5000股以上': '5000股以上',\n", "    'tr_low': '低换手率',\n", "    'tr_mid': '中换手率',\n", "    'tr_high': '高换手率',\n", "    'tr_extreme_high': '极高换手率'\n", "}\n", "\n", "def translate_field_name(field_name):\n", "    \"\"\"将英文字段名转换为中文\"\"\"\n", "    return field_name_mapping.get(field_name, field_name)\n", "\n", "def translate_group_values(df, column):\n", "    \"\"\"将分组值转换为中文\"\"\"\n", "    if column in df.columns:\n", "        df[column] = df[column].map(lambda x: field_name_mapping.get(str(x), str(x)))\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 时间序列数据处理函数\n", "def prepare_time_series_data(datas, group_field):\n", "    \"\"\"准备时间序列分析数据\"\"\"\n", "    # 按日期和分组字段进行聚合\n", "    time_series_data = datas.groupby(['date', group_field]).agg({\n", "        'executed_notional': 'sum',\n", "        'id': 'count',\n", "        'vwap_cost': lambda x: np.average(x, weights=datas.loc[x.index, 'executed_notional']),\n", "        'arrival_cost': lambda x: np.average(x, weights=datas.loc[x.index, 'executed_notional']),\n", "        'cplt_rate': 'mean',\n", "        'cxl_rate': 'mean'\n", "    }).reset_index()\n", "    \n", "    # 重命名列\n", "    time_series_data.columns = ['date', group_field, 'total_value', 'order_count', \n", "                               'avg_vwap_cost', 'avg_arrival_cost', 'avg_completion_rate', 'avg_cancel_rate']\n", "    \n", "    return time_series_data\n", "\n", "def generate_group_summary_table(datas, group_field):\n", "    \"\"\"生成分组汇总统计表\"\"\"\n", "    summary = show_by_name(datas, [group_field], stats)\n", "    \n", "    # 转换分组值为中文\n", "    summary = translate_group_values(summary, 'group')\n", "    \n", "    # 选择关键指标\n", "    key_columns = ['group', 'executed_value', 'no_of_porders', 'vwap_cost', \n", "                   'arrival_cost', 'avg_cpt_rate', 'cxl_rate', 'win_r']\n", "    summary_display = summary[key_columns].copy()\n", "    \n", "    # 格式化数值\n", "    summary_display['executed_value'] = summary_display['executed_value'].apply(lambda x: f'{x/10000:.1f}万')\n", "    summary_display['vwap_cost'] = summary_display['vwap_cost'].apply(lambda x: f'{x:.2f}bp')\n", "    summary_display['arrival_cost'] = summary_display['arrival_cost'].apply(lambda x: f'{x:.2f}bp')\n", "    summary_display['avg_cpt_rate'] = summary_display['avg_cpt_rate'].apply(lambda x: f'{x*100:.1f}%')\n", "    summary_display['cxl_rate'] = summary_display['cxl_rate'].apply(lambda x: f'{x*100:.1f}%')\n", "    summary_display['win_r'] = summary_display['win_r'].apply(lambda x: f'{x*100:.1f}%')\n", "    \n", "    # 重命名列为中文\n", "    summary_display.columns = ['分组', '成交金额', '订单数量', 'VWAP成本', '到达成本', '完成率', '撤单率', '胜率']\n", "    \n", "    return summary_display"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 图表绘制函数\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from matplotlib import rcParams\n", "\n", "# 设置中文字体\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']  # 用来正常显示中文标签\n", "plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号\n", "\n", "def plot_bar_chart(summary_data, group_field, title_suffix=''):\n", "    \"\"\"绘制柱状图\"\"\"\n", "    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))\n", "    fig.suptitle(f'{translate_field_name(group_field)}分组统计{title_suffix}', fontsize=16, fontweight='bold')\n", "    \n", "    # 成交金额柱状图\n", "    ax1.bar(summary_data['分组'], summary_data['成交金额'].str.replace('万', '').astype(float), color='steelblue')\n", "    ax1.set_title('成交金额分布', fontweight='bold')\n", "    ax1.set_ylabel('成交金额(万元)')\n", "    ax1.tick_params(axis='x', rotation=45)\n", "    \n", "    # 订单数量柱状图\n", "    ax2.bar(summary_data['分组'], summary_data['订单数量'], color='lightcoral')\n", "    ax2.set_title('订单数量分布', fontweight='bold')\n", "    ax2.set_ylabel('订单数量')\n", "    ax2.tick_params(axis='x', rotation=45)\n", "    \n", "    # VWAP成本柱状图\n", "    vwap_values = summary_data['VWAP成本'].str.replace('bp', '').astype(float)\n", "    colors = ['green' if x < 0 else 'red' for x in vwap_values]\n", "    ax3.bar(summary_data['分组'], vwap_values, color=colors)\n", "    ax3.set_title('VWAP成本分布', fontweight='bold')\n", "    ax3.set_ylabel('VWAP成本(bp)')\n", "    ax3.tick_params(axis='x', rotation=45)\n", "    ax3.axhline(y=0, color='black', linestyle='-', alpha=0.3)\n", "    \n", "    # 完成率柱状图\n", "    ax4.bar(summary_data['分组'], summary_data['完成率'].str.replace('%', '').astype(float), color='gold')\n", "    ax4.set_title('完成率分布', fontweight='bold')\n", "    ax4.set_ylabel('完成率(%)')\n", "    ax4.tick_params(axis='x', rotation=45)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "def plot_pie_chart(summary_data, group_field, metric='成交金额'):\n", "    \"\"\"绘制饼图\"\"\"\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "    fig.suptitle(f'{translate_field_name(group_field)}分组占比分析', fontsize=16, fontweight='bold')\n", "    \n", "    # 成交金额占比饼图\n", "    values1 = summary_data['成交金额'].str.replace('万', '').astype(float)\n", "    ax1.pie(values1, labels=summary_data['分组'], autopct='%1.1f%%', startangle=90)\n", "    ax1.set_title('成交金额占比', fontweight='bold')\n", "    \n", "    # 订单数量占比饼图\n", "    values2 = summary_data['订单数量']\n", "    ax2.pie(values2, labels=summary_data['分组'], autopct='%1.1f%%', startangle=90)\n", "    ax2.set_title('订单数量占比', fontweight='bold')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def plot_time_series(datas, group_field):\n", "    \"\"\"绘制时间序列图\"\"\"\n", "    time_data = prepare_time_series_data(datas, group_field)\n", "    \n", "    # 转换分组值为中文\n", "    time_data = translate_group_values(time_data, group_field)\n", "    \n", "    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))\n", "    fig.suptitle(f'{translate_field_name(group_field)}时间序列分析', fontsize=16, fontweight='bold')\n", "    \n", "    # 成交金额时间序列\n", "    for group in time_data[group_field].unique():\n", "        group_data = time_data[time_data[group_field] == group]\n", "        ax1.plot(group_data['date'], group_data['total_value']/10000, marker='o', label=group, linewidth=2)\n", "    ax1.set_title('成交金额时间序列', fontweight='bold')\n", "    ax1.set_ylabel('成交金额(万元)')\n", "    ax1.legend()\n", "    ax1.tick_params(axis='x', rotation=45)\n", "    ax1.grid(True, alpha=0.3)\n", "    \n", "    # 订单数量时间序列\n", "    for group in time_data[group_field].unique():\n", "        group_data = time_data[time_data[group_field] == group]\n", "        ax2.plot(group_data['date'], group_data['order_count'], marker='s', label=group, linewidth=2)\n", "    ax2.set_title('订单数量时间序列', fontweight='bold')\n", "    ax2.set_ylabel('订单数量')\n", "    ax2.legend()\n", "    ax2.tick_params(axis='x', rotation=45)\n", "    ax2.grid(True, alpha=0.3)\n", "    \n", "    # VWAP成本时间序列\n", "    for group in time_data[group_field].unique():\n", "        group_data = time_data[time_data[group_field] == group]\n", "        ax3.plot(group_data['date'], group_data['avg_vwap_cost'], marker='^', label=group, linewidth=2)\n", "    ax3.set_title('VWAP成本时间序列', fontweight='bold')\n", "    ax3.set_ylabel('VWAP成本(bp)')\n", "    ax3.legend()\n", "    ax3.tick_params(axis='x', rotation=45)\n", "    ax3.axhline(y=0, color='black', linestyle='--', alpha=0.5)\n", "    ax3.grid(True, alpha=0.3)\n", "    \n", "    # 完成率时间序列\n", "    for group in time_data[group_field].unique():\n", "        group_data = time_data[time_data[group_field] == group]\n", "        ax4.plot(group_data['date'], group_data['avg_completion_rate']*100, marker='d', label=group, linewidth=2)\n", "    ax4.set_title('完成率时间序列', fontweight='bold')\n", "    ax4.set_ylabel('完成率(%)')\n", "    ax4.legend()\n", "    ax4.tick_params(axis='x', rotation=45)\n", "    ax4.grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def generate_comprehensive_analysis(datas, group_field):\n", "    \"\"\"生成综合分析报告\"\"\"\n", "    print(f\"\\n{'='*60}\")\n", "    print(f\"  {translate_field_name(group_field)}分组综合分析报告\")\n", "    print(f\"{'='*60}\\n\")\n", "    \n", "    # 1. 生成汇总统计表\n", "    print(\"📊 分组统计汇总表\")\n", "    print(\"-\" * 40)\n", "    summary_table = generate_group_summary_table(datas, group_field)\n", "    print(summary_table.to_string(index=False))\n", "    print(\"\\n\")\n", "    \n", "    # 2. 绘制柱状图\n", "    print(\"📈 分组统计柱状图\")\n", "    print(\"-\" * 40)\n", "    plot_bar_chart(summary_table, group_field)\n", "    \n", "    # 3. 绘制饼图\n", "    print(\"🥧 分组占比饼图\")\n", "    print(\"-\" * 40)\n", "    plot_pie_chart(summary_table, group_field)\n", "    \n", "    # 4. 绘制时间序列图\n", "    print(\"📉 时间序列趋势图\")\n", "    print(\"-\" * 40)\n", "    plot_time_series(datas, group_field)\n", "    \n", "    print(f\"\\n✅ {translate_field_name(group_field)}分组分析完成！\\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 执行各分组的综合分析"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 确保数据已加载并处理完成\n", "# if 'datas' not in locals():\n", "    # 如果数据未加载，先加载数据\n", "    # datas = read_datas('20250615', '20250623')\n", "    # datas = datas_add_cons_group(datas)\n", "    # datas = datas_add_some_group(datas)\n", "    # datas = datas_add_turnover_rate_group(datas)\n", "\n", "print(f\"数据加载完成，共 {len(datas)} 条记录\")\n", "print(f\"时间范围：{datas['date'].min()} 至 {datas['date'].max()}\")\n", "print(f\"可用分组字段：{['index_name', 'price_group', 'interval_group', 'po_volume_group', 'turnover_group']}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 1. 指数成分分组分析\n", "generate_comprehensive_analysis(datas, 'index_name')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 2. 价格分组分析\n", "generate_comprehensive_analysis(datas, 'price_group')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 3. 时间间隔分组分析\n", "generate_comprehensive_analysis(datas, 'interval_group')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 4. 订单规模分组分析\n", "generate_comprehensive_analysis(datas, 'po_volume_group')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 5. 换手率分组分析\n", "generate_comprehensive_analysis(datas, 'turnover_group')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 总结报告"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 生成总体统计摘要\n", "def generate_overall_summary(datas):\n", "    \"\"\"生成总体统计摘要\"\"\"\n", "    print(\"\\n\" + \"=\"*80)\n", "    print(\"                        📈 TCA分析总体摘要报告 📈\")\n", "    print(\"=\"*80)\n", "    \n", "    total_orders = len(datas)\n", "    total_value = datas['executed_notional'].sum()\n", "    avg_vwap_bps = np.average(datas['vwap_cost'], weights=datas['executed_notional'] / total_value)\n", "    avg_arrival_bps = np.average(datas['arrival_cost'], weights=datas['executed_notional'] / total_value)\n", "    avg_completion_rate = datas['cplt_rate'].mean()\n", "    avg_cancel_rate = datas['cancel_slices'].sum() / (datas['total_slices'] - datas['error_slices']).sum()\n", "    win_rate = datas['win_rate'].mean()\n", "    \n", "    print(f\"\\n📊 基础统计：\")\n", "    print(f\"   • 总订单数：{total_orders:,} 笔\")\n", "    print(f\"   • 总成交金额：{total_value/100000000:.2f} 亿元\")\n", "    print(f\"   • 分析时间段：{datas['date'].min()} ~ {datas['date'].max()}\")\n", "    print(f\"   • 交易日数：{datas['date'].nunique()} 天\")\n", "    \n", "    print(f\"\\n💰 成本分析：\")\n", "    print(f\"   • 平均VWAP成本：{avg_vwap_bps:.2f} bp\")\n", "    print(f\"   • 平均到达成本：{avg_arrival_bps:.2f} bp\")\n", "    print(f\"   • VWAP总收益：{(datas['vwap_cost'] * datas['executed_notional'] / 10000).sum():,.0f} 元\")\n", "    \n", "    print(f\"\\n⚡ 执行效率：\")\n", "    print(f\"   • 平均完成率：{avg_completion_rate*100:.1f}%\")\n", "    print(f\"   • 平均撤单率：{avg_cancel_rate*100:.1f}%\")\n", "    print(f\"   • 胜率：{win_rate*100:.1f}%\")\n", "    \n", "    print(f\"\\n🏆 分组表现排名：\")\n", "    \n", "    # 各分组的最佳表现\n", "    group_fields = ['index_name', 'price_group', 'turnover_group']\n", "    for field in group_fields:\n", "        group_stats = show_by_name(datas, [field], stats)\n", "        best_vwap = group_stats.loc[group_stats['vwap_cost'].idxmin()]\n", "        print(f\"   • {translate_field_name(field)}最佳VWAP：{translate_field_name(best_vwap['group'])} ({best_vwap['vwap_cost']:.2f}bp)\")\n", "    \n", "    print(\"\\n\" + \"=\"*80)\n", "    print(\"                           ✅ 分析报告完成 ✅\")\n", "    print(\"=\"*80 + \"\\n\")\n", "\n", "# 执行总体摘要\n", "generate_overall_summary(datas)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 4}