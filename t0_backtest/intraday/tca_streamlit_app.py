import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import datetime
from datetime import timedelta
import warnings
import io
import base64
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import plotly.io as pio
warnings.filterwarnings('ignore')

# 导入原有的工具函数
import sys
import os
from pathlib import Path

# 添加路径以导入原有模块
cur_dir = Path.cwd()
p_dir = os.path.join(cur_dir, '../../')
sys.path.insert(0, p_dir)

try:
    from utils import mysql
    from data_utils.trading_calendar import Calendar
    from misc import tools
except ImportError as e:
    st.error(f"导入模块失败: {e}")
    st.stop()

# 页面配置
st.set_page_config(
    page_title="TCA交易成本分析系统",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS样式
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
    .section-header {
        font-size: 1.5rem;
        font-weight: bold;
        color: #2c3e50;
        margin-top: 2rem;
        margin-bottom: 1rem;
    }
</style>
""", unsafe_allow_html=True)

# 中文字段映射字典
FIELD_NAME_MAPPING = {
    # 分组字段
    'index_name': '指数成分',
    'price_group': '价格分组',
    'interval_group': '时间间隔',
    'po_volume_group': '订单规模',
    'turnover_group': '换手率分组',
    'date': '日期',
    
    # 统计指标
    'executed_value': '成交金额',
    'no_of_porders': '订单数量',
    'po_amt_mean': '平均订单金额',
    'po_amt_med': '订单金额中位数',
    'po_lots_mean': '平均订单手数',
    'po_lots_med': '订单手数中位数',
    'arrival_cost': '到达成本',
    'open_cost': '开盘成本',
    'close_cost': '收盘成本',
    'vwap_cost': 'VWAP成本',
    'vwap_cost2': 'VWAP成本2',
    'avg_cpt_rate': '平均完成率',
    'wavg_cpt_rate': '加权平均完成率',
    'cxl_rate': '撤单率',
    'target_exe_time': '目标执行时间',
    'avg_order_size': '平均订单规模',
    'avg_order_value': '平均订单价值',
    'win_r': '胜率',
    'num_order': '订单总数',
    
    # 分组值映射
    'ZZ1000': '中证1000',
    'ZZ2000': '中证2000', 
    'HS300': '沪深300',
    'OTHER': '其他',
    'extreme_low': '极低价',
    'low': '低价',
    'mid': '中价',
    'high': '高价',
    'extreme_high': '极高价',
    '0<t<=15m': '15分钟内',
    '15m<t<=30m': '15-30分钟',
    '30m<t<=1h': '30分钟-1小时',
    '1h<t<=4h': '1-4小时',
    '100-500股': '100-500股',
    '500-1000股': '500-1000股',
    '1000-5000股': '1000-5000股',
    '5000股以上': '5000股以上',
    'tr_low': '低换手率',
    'tr_mid': '中换手率',
    'tr_high': '高换手率',
    'tr_extreme_high': '极高换手率'
}

def translate_field_name(field_name):
    """将英文字段名转换为中文"""
    return FIELD_NAME_MAPPING.get(field_name, field_name)

def translate_group_values(df, column):
    """将分组值转换为中文"""
    if column in df.columns:
        df[column] = df[column].map(lambda x: FIELD_NAME_MAPPING.get(str(x), str(x)))
    return df

# 缓存数据加载函数
@st.cache_data(ttl=3600)  # 缓存1小时
def get_available_filters():
    """获取可用的筛选选项"""
    try:
        # 获取日期范围，添加日期格式过滤
        date_sql = """
        SELECT MIN(date) as min_date, MAX(date) as max_date
        FROM algo_parentorder
        WHERE date >= '20200101' AND date <= '20301231'
        """
        date_result = mysql.query(mysql.get_zs_trading_data_db_connection(), date_sql)

        # 检查日期数据是否有效
        if date_result.empty or date_result['min_date'].iloc[0] is None:
            # 如果没有有效数据，使用默认日期范围
            min_date = datetime.date(2024, 1, 1)
            max_date = datetime.date(2024, 12, 31)
        else:
            min_date = pd.to_datetime(str(date_result['min_date'].iloc[0])).date()
            max_date = pd.to_datetime(str(date_result['max_date'].iloc[0])).date()

            # 验证日期范围的合理性
            if min_date.year < 2020 or max_date.year > 2030:
                min_date = datetime.date(2024, 1, 1)
                max_date = datetime.date(2024, 12, 31)

        # 获取账户列表
        account_sql = "SELECT DISTINCT account_id FROM algo_parentorder WHERE account_id IS NOT NULL AND account_id != '' ORDER BY account_id"
        accounts = mysql.query(mysql.get_zs_trading_data_db_connection(), account_sql)

        # 获取公司列表
        firm_sql = "SELECT DISTINCT firm FROM algo_parentorder WHERE firm IS NOT NULL AND firm != '' ORDER BY firm"
        firms = mysql.query(mysql.get_zs_trading_data_db_connection(), firm_sql)

        # 获取券商列表
        broker_sql = "SELECT DISTINCT broker FROM algo_parentorder WHERE broker IS NOT NULL AND broker != '' ORDER BY broker"
        brokers = mysql.query(mysql.get_zs_trading_data_db_connection(), broker_sql)

        # 获取算法提供商列表
        algo_provider_sql = "SELECT DISTINCT algo_provider FROM algo_parentorder WHERE algo_provider IS NOT NULL AND algo_provider != '' ORDER BY algo_provider"
        algo_providers = mysql.query(mysql.get_zs_trading_data_db_connection(), algo_provider_sql)

        return {
            'date_range': (min_date, max_date),
            'accounts': accounts['account_id'].tolist() if not accounts.empty else [],
            'firms': firms['firm'].tolist() if not firms.empty else [],
            'brokers': brokers['broker'].tolist() if not brokers.empty else [],
            'algo_providers': algo_providers['algo_provider'].tolist() if not algo_providers.empty else []
        }
    except Exception as e:
        st.error(f"获取筛选选项失败: {e}")
        # 返回默认值
        return {
            'date_range': (datetime.date(2024, 1, 1), datetime.date(2024, 12, 31)),
            'accounts': [],
            'firms': [],
            'brokers': [],
            'algo_providers': []
        }

def main():
    """主函数"""
    # 页面标题
    st.markdown('<h1 class="main-header">📊 TCA交易成本分析系统</h1>', unsafe_allow_html=True)
    
    # 获取筛选选项
    filters = get_available_filters()
    if filters is None:
        st.error("无法连接到数据库，请检查数据库连接配置")
        return
    
    # 侧边栏筛选条件
    st.sidebar.markdown("## 🔍 数据筛选条件")
    
    # 日期范围选择
    st.sidebar.markdown("### 📅 时间范围")
    min_date = filters['date_range'][0]
    max_date = filters['date_range'][1]

    # 确保默认值在有效范围内
    default_start = max(min_date, max_date - timedelta(days=7))
    default_end = max_date

    date_range = st.sidebar.date_input(
        "选择分析时间范围",
        value=(default_start, default_end),
        min_value=min_date,
        max_value=max_date,
        format="YYYY-MM-DD"
    )
    
    if len(date_range) != 2:
        st.sidebar.warning("请选择完整的日期范围")
        return
    
    start_date, end_date = date_range
    
    # 账户选择
    st.sidebar.markdown("### 👤 账户筛选")
    selected_accounts = st.sidebar.multiselect(
        "选择账户ID（可多选）",
        options=filters['accounts'],
        default=None,
        help="留空表示选择所有账户"
    )
    
    # 公司选择
    st.sidebar.markdown("### 🏢 公司筛选")
    selected_firms = st.sidebar.multiselect(
        "选择公司（可多选）",
        options=filters['firms'],
        default=None,
        help="留空表示选择所有公司"
    )
    
    # 券商选择
    st.sidebar.markdown("### 🏦 券商筛选")
    selected_brokers = st.sidebar.multiselect(
        "选择券商（可多选）",
        options=filters['brokers'],
        default=None,
        help="留空表示选择所有券商"
    )
    
    # 算法提供商选择
    st.sidebar.markdown("### ⚙️ 算法提供商")
    selected_algo_providers = st.sidebar.multiselect(
        "选择算法提供商（可多选）",
        options=filters['algo_providers'],
        default=None,
        help="留空表示选择所有算法提供商"
    )
    
    # 分析按钮
    st.sidebar.markdown("---")
    if st.sidebar.button("🚀 开始分析", type="primary", use_container_width=True):
        # 存储筛选条件到session state
        st.session_state.analysis_params = {
            'start_date': start_date.strftime('%Y%m%d'),
            'end_date': end_date.strftime('%Y%m%d'),
            'accounts': selected_accounts,
            'firms': selected_firms,
            'brokers': selected_brokers,
            'algo_providers': selected_algo_providers
        }
        st.session_state.run_analysis = True
        st.rerun()
    
    # 显示当前筛选条件
    st.sidebar.markdown("---")
    st.sidebar.markdown("### 📋 当前筛选条件")
    st.sidebar.write(f"**时间范围:** {start_date} 至 {end_date}")
    st.sidebar.write(f"**账户数量:** {len(selected_accounts) if selected_accounts else '全部'}")
    st.sidebar.write(f"**公司数量:** {len(selected_firms) if selected_firms else '全部'}")
    st.sidebar.write(f"**券商数量:** {len(selected_brokers) if selected_brokers else '全部'}")
    st.sidebar.write(f"**算法提供商数量:** {len(selected_algo_providers) if selected_algo_providers else '全部'}")
    
    # 主页面内容
    if hasattr(st.session_state, 'run_analysis') and st.session_state.run_analysis:
        # 运行分析
        run_tca_analysis()
    else:
        # 显示欢迎页面
        show_welcome_page()

def show_welcome_page():
    """显示欢迎页面"""
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        st.markdown("""
        ### 🎯 系统功能
        
        **TCA交易成本分析系统**为您提供全面的交易执行分析：
        
        - 📊 **多维度分析**: 按指数成分、价格分组、时间间隔等维度分析
        - 📈 **交互式图表**: 使用Plotly提供丰富的交互式可视化
        - 🔍 **灵活筛选**: 支持按时间、账户、公司、券商等条件筛选
        - 📄 **报告导出**: 支持将分析结果导出为PDF报告
        
        ### 🚀 开始使用
        
        1. 在左侧边栏设置筛选条件
        2. 点击"开始分析"按钮
        3. 查看详细的分析结果
        4. 导出分析报告
        
        ### 📋 分析指标
        
        - **VWAP成本**: 相对于VWAP的交易成本
        - **到达成本**: 相对于到达价格的交易成本  
        - **完成率**: 订单执行完成度
        - **撤单率**: 订单撤销比例
        - **胜率**: 交易盈利订单占比
        """)

def run_tca_analysis():
    """运行TCA分析"""
    st.markdown('<h2 class="section-header">📊 TCA分析结果</h2>', unsafe_allow_html=True)
    
    # 显示分析参数
    params = st.session_state.analysis_params
    
    with st.expander("📋 分析参数", expanded=False):
        col1, col2 = st.columns(2)
        with col1:
            st.write(f"**时间范围:** {params['start_date']} - {params['end_date']}")
            st.write(f"**账户:** {len(params['accounts']) if params['accounts'] else '全部'}")
        with col2:
            st.write(f"**公司:** {len(params['firms']) if params['firms'] else '全部'}")
            st.write(f"**券商:** {len(params['brokers']) if params['brokers'] else '全部'}")
    
    # 加载和处理数据
    with st.spinner("正在加载和处理数据..."):
        data = load_and_process_data(params)
    
    if data is None or len(data) == 0:
        st.warning("根据当前筛选条件未找到数据，请调整筛选条件后重试。")
        return
    
    # 显示基本统计信息
    show_basic_statistics(data)

    # 显示分组分析
    show_group_analysis(data)

    # 添加PDF导出功能
    st.markdown("---")
    st.markdown("### 📄 报告导出")
    add_pdf_export_button(data)

# 数据处理函数
def fmt_td(delta):
    """格式化时间差"""
    total_seconds = int(delta.total_seconds())
    hours, remainder = divmod(total_seconds, 3600)
    minutes, seconds = divmod(remainder, 60)
    formatted_time = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
    return formatted_time

def show_by_name(datas, groups, stats_func):
    """按分组统计数据"""
    l = []
    for gn in groups:
        if gn == 'total':
            r = stats_func(datas)
            r['group'] = gn
            l.append(r)
        else:
            for n, g in datas.groupby(gn):
                r = stats_func(g)
                r['group'] = n
                l.append(r)
    return pd.DataFrame(l)

def stats(datas):
    """计算统计指标"""
    d = {}
    d['executed_value'] = datas['executed_notional'].sum()
    d['no_of_porders'] = len(datas)
    d['po_amt_mean'] = (datas['arrival_px'] * datas['quantity']).mean()
    d['po_amt_med'] = (datas['arrival_px'] * datas['quantity']).median()
    d['po_lots_mean'] = (datas['quantity'] / datas['lot_size']).mean()
    d['po_lots_med'] = (datas['quantity'] / datas['lot_size']).median()
    d['arrival_cost'] = -np.average(datas['arrival_cost'], weights=datas['executed_notional'] / d['executed_value'])
    d['open_cost'] = -np.average(datas['open_cost'], weights=datas['executed_notional'] / d['executed_value'])
    d['close_cost'] = -np.average(datas['close_cost'], weights=datas['executed_notional'] / d['executed_value'])
    d['vwap_cost'] = -np.average(datas['vwap_cost'], weights=datas['executed_notional'] / d['executed_value'])
    d['vwap_cost2'] = -np.average(datas['vwap_cost2'], weights=datas['executed_notional2'] / datas['executed_notional2'].sum())
    d['avg_cpt_rate'] = datas['cplt_rate'].mean()
    d['wavg_cpt_rate'] = np.average(datas['cplt_rate'], weights=(datas['arrival_px'] * datas['quantity']) / (datas['arrival_px'] * datas['quantity']).sum())
    if datas['cancel_slices'].sum() == 0:
        d['cxl_rate'] = 0
    else:
        d['cxl_rate'] = datas['cancel_slices'].sum() / (datas['total_slices'] - datas['error_slices']).sum()
    d['target_exe_time'] = fmt_td((datas['target_exe_time']).mean())
    d['avg_order_size'] = datas['avg_order_size'].mean()
    d['avg_order_value'] = (datas['executed_notional'] / (datas['total_slices'] - datas['error_slices'])).mean()
    d['win_r'] = datas['win_rate'].mean()
    d['num_order'] = len(datas)
    return d

@st.cache_data(ttl=1800)  # 缓存30分钟
def load_and_process_data(params):
    """加载和处理数据"""
    try:
        # 构建SQL查询
        sql = f"SELECT * FROM algo_parentorder WHERE date >= '{params['start_date']}' AND date <= '{params['end_date']}'"

        # 添加筛选条件
        if params['accounts']:
            account_list = "', '".join(str(acc) for acc in params['accounts'])
            sql += f" AND account_id IN ('{account_list}')"

        if params['firms']:
            firm_list = "', '".join(str(firm) for firm in params['firms'])
            sql += f" AND firm IN ('{firm_list}')"

        if params['brokers']:
            broker_list = "', '".join(str(broker) for broker in params['brokers'])
            sql += f" AND broker IN ('{broker_list}')"

        if params['algo_providers']:
            provider_list = "', '".join(str(provider) for provider in params['algo_providers'])
            sql += f" AND algo_provider IN ('{provider_list}')"

        # 执行查询
        pos = mysql.query(mysql.get_zs_trading_data_db_connection(), sql)

        if len(pos) == 0:
            return None

        # 获取TCA分析数据，处理单个ID的情况
        id_list = pos['id'].tolist()
        if len(id_list) == 1:
            id_condition = f"= {id_list[0]}"
        else:
            id_condition = f"IN {tuple(id_list)}"

        tca1 = mysql.query(mysql.get_zs_trading_data_db_connection(),
                          f"SELECT * FROM tca_basic_analysis WHERE id {id_condition}")
        tca2 = mysql.query(mysql.get_zs_trading_data_db_connection(),
                          f"SELECT * FROM tca_other_analysis WHERE id {id_condition}")
        tca3 = mysql.query(mysql.get_zs_trading_data_db_connection(),
                          f"SELECT * FROM tca_open_analysis WHERE id {id_condition}")

        # 合并数据
        datas = pos.merge(tca1, on='id', how='left').merge(tca2, on='id', how='left').merge(tca3, on='id', how='left')
        datas = datas.fillna(0)

        # 数据处理
        datas['date'] = datas['date'].astype(str)
        datas['executed_notional'] = datas['filled_quantity'] * datas['filled_price']
        datas['start_time'] = pd.to_datetime(datas['date'] + datas['start_time'].apply(lambda x: str(x).zfill(6)))
        datas['end_time'] = pd.to_datetime(datas['date'] + datas['end_time'].apply(lambda x: str(x).zfill(6)))
        datas['target_exe_time'] = datas['end_time'] - datas['start_time']
        datas['filled_price2'] = np.where((datas['filled_quantity'] < datas['quantity']) & (datas['oppo_px'] > 0),
                                         ((datas['filled_quantity'] * datas['filled_price']) + datas['oppo_px'] * (datas['quantity'] - datas['filled_quantity'])) / (datas['quantity']),
                                         datas['filled_price'])
        datas['executed_notional2'] = datas['filled_price2'] * datas['quantity']
        datas['vwap_cost2'] = (datas['side'] * (datas['filled_price2'] / datas['vwap'] - 1) * 10000).fillna(0)
        datas['open_cost'] = (datas['side'] * (datas['filled_price'] / datas['open_px'] - 1) * 10000).fillna(0)
        datas['close_cost'] = (datas['side'] * (datas['filled_price'] / datas['close_px'] - 1) * 10000).fillna(0)
        datas['lot_size'] = datas['symbol'].apply(lambda x: 200 if x[:3] == '688' else 100)
        datas = datas[datas['side'] != 0]

        # 添加分组信息
        datas = add_group_info(datas)

        return datas

    except Exception as e:
        st.error(f"数据加载失败: {e}")
        return None

def add_group_info(datas):
    """添加分组信息"""
    # 添加指数成分分组
    l = []
    dates = datas['date'].unique()
    for date in dates:
        try:
            cons_tag = tools.get_cons_tag_series(date)
            cons = cons_tag.to_frame('index_name').reset_index()
            cons['ticker'] = cons['ticker'].astype(str).str.zfill(6)
            cons.rename(columns={'ticker': 'symbol'}, inplace=True)
            cons['date'] = date
            l.append(cons)
        except:
            pass

    if len(l) > 0:
        cons = pd.concat(l, axis=0, ignore_index=True)
        datas = datas.merge(cons, on=['symbol', 'date'], how='left')

    datas['index_name'].fillna('OTHER', inplace=True)

    # 添加价格分组
    bins = [0.0, 2.00, 4.00, 10.00, 20.00, 9999.00]
    labels = ['extreme_low', 'low', 'mid', 'high', 'extreme_high']
    datas['price_group'] = pd.cut(datas['close_px'], bins=bins, labels=labels, right=False)

    # 添加时间间隔分组
    bins = [pd.Timedelta(minutes=i) for i in [0, 15, 30, 60, 240]]
    labels = ['0<t<=15m', '15m<t<=30m', '30m<t<=1h', '1h<t<=4h']
    datas['interval_group'] = pd.cut(datas['target_exe_time'], bins=bins, labels=labels, right=True)

    # 添加订单规模分组
    bins = [0, 500, 1000, 5000, 9999999]
    labels = ['100-500股', '500-1000股', '1000-5000股', '5000股以上']
    datas['po_volume_group'] = pd.cut(datas['quantity'], bins=bins, labels=labels, right=True)

    # 添加换手率分组
    l = []
    for date in dates:
        try:
            tag = tools.get_turnover_rate_by_date(date)
            tag['ticker'] = tag['ticker'].astype(str).str.zfill(6)
            tag.rename(columns={'ticker': 'symbol'}, inplace=True)
            tag['date'] = date
            l.append(tag)
        except:
            pass

    if len(l) > 0:
        tag = pd.concat(l, axis=0, ignore_index=True)
        datas = datas.merge(tag, on=['symbol', 'date'], how='left')
        bins = [0, 0.01, 0.10, 0.20, 1.00]
        labels = ['tr_low', 'tr_mid', 'tr_high', 'tr_extreme_high']
        datas['turnover_group'] = pd.cut(datas['turnover_rate'], bins=bins, labels=labels, right=False)
    else:
        datas['turnover_group'] = 'tr_mid'

    return datas

def show_basic_statistics(data):
    """显示基本统计信息"""
    st.markdown('<h3 class="section-header">📈 基本统计信息</h3>', unsafe_allow_html=True)

    # 计算基本指标
    total_orders = len(data)
    total_value = data['executed_notional'].sum()
    avg_vwap_bps = np.average(data['vwap_cost'], weights=data['executed_notional'] / total_value)
    avg_arrival_bps = np.average(data['arrival_cost'], weights=data['executed_notional'] / total_value)
    avg_completion_rate = data['cplt_rate'].mean()
    avg_cancel_rate = data['cancel_slices'].sum() / (data['total_slices'] - data['error_slices']).sum() if (data['total_slices'] - data['error_slices']).sum() > 0 else 0
    win_rate = data['win_rate'].mean()

    # 显示关键指标卡片
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric(
            label="📊 总订单数",
            value=f"{total_orders:,}",
            help="分析期间的总订单数量"
        )

    with col2:
        st.metric(
            label="💰 总成交金额",
            value=f"{total_value/100000000:.2f}亿",
            help="分析期间的总成交金额"
        )

    with col3:
        st.metric(
            label="📉 平均VWAP成本",
            value=f"{avg_vwap_bps:.2f}bp",
            delta=f"{'盈利' if avg_vwap_bps < 0 else '亏损'}",
            help="相对于VWAP的平均交易成本"
        )

    with col4:
        st.metric(
            label="⚡ 平均完成率",
            value=f"{avg_completion_rate*100:.1f}%",
            help="订单平均执行完成率"
        )

    # 第二行指标
    col5, col6, col7, col8 = st.columns(4)

    with col5:
        st.metric(
            label="📈 到达成本",
            value=f"{avg_arrival_bps:.2f}bp",
            help="相对于到达价格的平均交易成本"
        )

    with col6:
        st.metric(
            label="❌ 撤单率",
            value=f"{avg_cancel_rate*100:.1f}%",
            help="订单撤销比例"
        )

    with col7:
        st.metric(
            label="🏆 胜率",
            value=f"{win_rate*100:.1f}%",
            help="盈利订单占比"
        )

    with col8:
        st.metric(
            label="📅 分析天数",
            value=f"{data['date'].nunique()}天",
            help="分析的交易日数量"
        )

    # 时间序列趋势图
    st.markdown("#### 📊 每日交易概览")

    daily_summary = data.groupby('date').agg({
        'executed_notional': 'sum',
        'vwap_cost': lambda x: np.average(x, weights=data.loc[x.index, 'executed_notional']),
        'id': 'count'
    }).reset_index()

    daily_summary.columns = ['date', 'total_value', 'avg_vwap', 'order_count']
    daily_summary['date'] = pd.to_datetime(daily_summary['date'])

    # 创建双轴图表
    fig = make_subplots(
        rows=2, cols=1,
        subplot_titles=('每日成交金额和订单数量', '每日VWAP成本'),
        specs=[[{"secondary_y": True}], [{"secondary_y": False}]],
        vertical_spacing=0.1
    )

    # 成交金额柱状图
    fig.add_trace(
        go.Bar(
            x=daily_summary['date'],
            y=daily_summary['total_value'] / 10000,
            name='成交金额(万元)',
            marker_color='lightblue',
            yaxis='y'
        ),
        row=1, col=1
    )

    # 订单数量折线图
    fig.add_trace(
        go.Scatter(
            x=daily_summary['date'],
            y=daily_summary['order_count'],
            mode='lines+markers',
            name='订单数量',
            line=dict(color='red', width=2),
            yaxis='y2'
        ),
        row=1, col=1
    )

    # VWAP成本折线图
    fig.add_trace(
        go.Scatter(
            x=daily_summary['date'],
            y=daily_summary['avg_vwap'],
            mode='lines+markers',
            name='VWAP成本(bp)',
            line=dict(color='green', width=2),
            fill='tonexty' if daily_summary['avg_vwap'].iloc[0] < 0 else 'tozeroy',
            fillcolor='rgba(0,255,0,0.1)' if daily_summary['avg_vwap'].mean() < 0 else 'rgba(255,0,0,0.1)'
        ),
        row=2, col=1
    )

    # 添加零线
    fig.add_hline(y=0, line_dash="dash", line_color="black", opacity=0.5, row=2, col=1)

    # 更新布局
    fig.update_layout(
        height=600,
        showlegend=True,
        title_text="每日交易表现概览"
    )

    # 设置y轴标题
    fig.update_yaxes(title_text="成交金额(万元)", row=1, col=1)
    fig.update_yaxes(title_text="订单数量", secondary_y=True, row=1, col=1)
    fig.update_yaxes(title_text="VWAP成本(bp)", row=2, col=1)

    st.plotly_chart(fig, use_container_width=True)

def show_group_analysis(data):
    """显示分组分析"""
    st.markdown('<h3 class="section-header">🔍 分组分析</h3>', unsafe_allow_html=True)

    # 分组选择
    group_options = {
        'index_name': '指数成分分组',
        'price_group': '价格分组',
        'interval_group': '时间间隔分组',
        'po_volume_group': '订单规模分组',
        'turnover_group': '换手率分组'
    }

    selected_group = st.selectbox(
        "选择分析维度",
        options=list(group_options.keys()),
        format_func=lambda x: group_options[x],
        index=0
    )

    # 生成分组分析
    generate_group_analysis(data, selected_group)

def generate_group_analysis(data, group_field):
    """生成特定分组的分析"""
    # 计算分组统计
    group_stats = show_by_name(data, [group_field], stats)

    # 转换分组值为中文
    group_stats = translate_group_values(group_stats, 'group')

    # 显示统计表格
    st.markdown(f"#### 📋 {translate_field_name(group_field)}统计表")

    # 格式化显示表格
    display_df = group_stats[['group', 'executed_value', 'no_of_porders', 'vwap_cost',
                             'arrival_cost', 'avg_cpt_rate', 'cxl_rate', 'win_r']].copy()

    display_df['executed_value'] = display_df['executed_value'].apply(lambda x: f'{x/10000:.1f}万')
    display_df['vwap_cost'] = display_df['vwap_cost'].apply(lambda x: f'{x:.2f}bp')
    display_df['arrival_cost'] = display_df['arrival_cost'].apply(lambda x: f'{x:.2f}bp')
    display_df['avg_cpt_rate'] = display_df['avg_cpt_rate'].apply(lambda x: f'{x*100:.1f}%')
    display_df['cxl_rate'] = display_df['cxl_rate'].apply(lambda x: f'{x*100:.1f}%')
    display_df['win_r'] = display_df['win_r'].apply(lambda x: f'{x*100:.1f}%')

    display_df.columns = ['分组', '成交金额', '订单数量', 'VWAP成本', '到达成本', '完成率', '撤单率', '胜率']

    st.dataframe(display_df, use_container_width=True, hide_index=True)

    # 绘制图表
    create_group_charts(group_stats, group_field)

def create_group_charts(group_stats, group_field):
    """创建分组图表"""
    # 转换数据为数值类型用于绘图
    chart_data = group_stats.copy()

    # 创建图表标签页
    tab1, tab2, tab3, tab4 = st.tabs(["📊 柱状图", "🥧 饼图", "📈 时间序列", "📋 详细数据"])

    with tab1:
        st.markdown("#### 📊 分组指标柱状图")
        create_bar_charts(chart_data, group_field)

    with tab2:
        st.markdown("#### 🥧 分组占比饼图")
        create_pie_charts(chart_data, group_field)

    with tab3:
        st.markdown("#### 📈 时间序列分析")
        create_time_series_charts(chart_data, group_field)

    with tab4:
        st.markdown("#### 📋 详细统计数据")
        st.dataframe(group_stats, use_container_width=True, hide_index=True)

def create_bar_charts(data, group_field):
    """创建柱状图"""
    # 转换分组值为中文
    data = translate_group_values(data, 'group')

    # 创建子图
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=('成交金额分布', '订单数量分布', 'VWAP成本分布', '完成率分布'),
        specs=[[{"type": "bar"}, {"type": "bar"}],
               [{"type": "bar"}, {"type": "bar"}]]
    )

    # 成交金额柱状图
    fig.add_trace(
        go.Bar(
            x=data['group'],
            y=data['executed_value'] / 10000,
            name='成交金额(万元)',
            marker_color='steelblue',
            showlegend=False
        ),
        row=1, col=1
    )

    # 订单数量柱状图
    fig.add_trace(
        go.Bar(
            x=data['group'],
            y=data['no_of_porders'],
            name='订单数量',
            marker_color='lightcoral',
            showlegend=False
        ),
        row=1, col=2
    )

    # VWAP成本柱状图
    colors = ['green' if x < 0 else 'red' for x in data['vwap_cost']]
    fig.add_trace(
        go.Bar(
            x=data['group'],
            y=data['vwap_cost'],
            name='VWAP成本(bp)',
            marker_color=colors,
            showlegend=False
        ),
        row=2, col=1
    )

    # 完成率柱状图
    fig.add_trace(
        go.Bar(
            x=data['group'],
            y=data['avg_cpt_rate'] * 100,
            name='完成率(%)',
            marker_color='gold',
            showlegend=False
        ),
        row=2, col=2
    )

    # 添加零线到VWAP成本图
    fig.add_hline(y=0, line_dash="dash", line_color="black", opacity=0.5, row=2, col=1)

    # 更新布局
    fig.update_layout(
        height=600,
        title_text=f"{translate_field_name(group_field)}分组统计柱状图",
        showlegend=False
    )

    # 更新y轴标题
    fig.update_yaxes(title_text="成交金额(万元)", row=1, col=1)
    fig.update_yaxes(title_text="订单数量", row=1, col=2)
    fig.update_yaxes(title_text="VWAP成本(bp)", row=2, col=1)
    fig.update_yaxes(title_text="完成率(%)", row=2, col=2)

    # 旋转x轴标签
    fig.update_xaxes(tickangle=45)

    st.plotly_chart(fig, use_container_width=True)

def create_pie_charts(data, group_field):
    """创建饼图"""
    # 转换分组值为中文
    data = translate_group_values(data, 'group')

    col1, col2 = st.columns(2)

    with col1:
        # 成交金额占比饼图
        fig1 = go.Figure(data=[go.Pie(
            labels=data['group'],
            values=data['executed_value'],
            hole=0.3,
            textinfo='label+percent',
            textposition='outside'
        )])

        fig1.update_layout(
            title=f"{translate_field_name(group_field)} - 成交金额占比",
            height=400
        )

        st.plotly_chart(fig1, use_container_width=True)

    with col2:
        # 订单数量占比饼图
        fig2 = go.Figure(data=[go.Pie(
            labels=data['group'],
            values=data['no_of_porders'],
            hole=0.3,
            textinfo='label+percent',
            textposition='outside'
        )])

        fig2.update_layout(
            title=f"{translate_field_name(group_field)} - 订单数量占比",
            height=400
        )

        st.plotly_chart(fig2, use_container_width=True)

def create_time_series_charts(data, group_field):
    """创建时间序列图表"""
    st.info("时间序列分析需要按日期分组的数据，当前显示的是总体分组统计。")

    # 这里可以添加更复杂的时间序列分析
    # 暂时显示一个简单的趋势图
    fig = go.Figure()

    # 添加VWAP成本趋势
    fig.add_trace(go.Scatter(
        x=data['group'],
        y=data['vwap_cost'],
        mode='lines+markers',
        name='VWAP成本(bp)',
        line=dict(color='blue', width=3),
        marker=dict(size=8)
    ))

    # 添加零线
    fig.add_hline(y=0, line_dash="dash", line_color="black", opacity=0.5)

    fig.update_layout(
        title=f"{translate_field_name(group_field)} - VWAP成本趋势",
        xaxis_title="分组",
        yaxis_title="VWAP成本(bp)",
        height=400
    )

    st.plotly_chart(fig, use_container_width=True)

def generate_pdf_report(data, analysis_params):
    """生成PDF报告"""
    buffer = io.BytesIO()

    # 创建PDF文档
    doc = SimpleDocTemplate(buffer, pagesize=A4)
    story = []

    # 获取样式
    styles = getSampleStyleSheet()
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=30,
        alignment=1  # 居中
    )

    # 标题
    story.append(Paragraph("TCA交易成本分析报告", title_style))
    story.append(Spacer(1, 12))

    # 分析参数
    story.append(Paragraph("分析参数", styles['Heading2']))
    param_data = [
        ['参数', '值'],
        ['时间范围', f"{analysis_params['start_date']} - {analysis_params['end_date']}"],
        ['账户数量', f"{len(analysis_params['accounts']) if analysis_params['accounts'] else '全部'}"],
        ['公司数量', f"{len(analysis_params['firms']) if analysis_params['firms'] else '全部'}"],
        ['券商数量', f"{len(analysis_params['brokers']) if analysis_params['brokers'] else '全部'}"],
        ['算法提供商数量', f"{len(analysis_params['algo_providers']) if analysis_params['algo_providers'] else '全部'}"]
    ]

    param_table = Table(param_data)
    param_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 14),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))

    story.append(param_table)
    story.append(Spacer(1, 12))

    # 基本统计
    story.append(Paragraph("基本统计信息", styles['Heading2']))

    total_orders = len(data)
    total_value = data['executed_notional'].sum()
    avg_vwap_bps = np.average(data['vwap_cost'], weights=data['executed_notional'] / total_value)
    avg_completion_rate = data['cplt_rate'].mean()
    win_rate = data['win_rate'].mean()

    stats_data = [
        ['指标', '值'],
        ['总订单数', f"{total_orders:,}"],
        ['总成交金额', f"{total_value/100000000:.2f}亿元"],
        ['平均VWAP成本', f"{avg_vwap_bps:.2f}bp"],
        ['平均完成率', f"{avg_completion_rate*100:.1f}%"],
        ['胜率', f"{win_rate*100:.1f}%"]
    ]

    stats_table = Table(stats_data)
    stats_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 14),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))

    story.append(stats_table)
    story.append(Spacer(1, 12))

    # 分组分析
    for group_field in ['index_name', 'price_group', 'turnover_group']:
        story.append(PageBreak())
        story.append(Paragraph(f"{translate_field_name(group_field)}分组分析", styles['Heading2']))

        group_stats = show_by_name(data, [group_field], stats)
        group_stats = translate_group_values(group_stats, 'group')

        # 创建分组统计表格
        table_data = [['分组', '成交金额(万)', '订单数', 'VWAP成本(bp)', '完成率(%)', '胜率(%)']]

        for _, row in group_stats.iterrows():
            table_data.append([
                str(row['group']),
                f"{row['executed_value']/10000:.1f}",
                str(int(row['no_of_porders'])),
                f"{row['vwap_cost']:.2f}",
                f"{row['avg_cpt_rate']*100:.1f}",
                f"{row['win_r']*100:.1f}"
            ])

        group_table = Table(table_data)
        group_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))

        story.append(group_table)
        story.append(Spacer(1, 12))

    # 生成PDF
    doc.build(story)
    buffer.seek(0)
    return buffer

def add_pdf_export_button(data):
    """添加PDF导出按钮"""
    if st.button("📄 导出PDF报告", type="secondary", use_container_width=True):
        with st.spinner("正在生成PDF报告..."):
            try:
                pdf_buffer = generate_pdf_report(data, st.session_state.analysis_params)

                # 创建下载链接
                b64 = base64.b64encode(pdf_buffer.read()).decode()
                href = f'<a href="data:application/pdf;base64,{b64}" download="TCA分析报告_{datetime.datetime.now().strftime("%Y%m%d_%H%M%S")}.pdf">点击下载PDF报告</a>'
                st.markdown(href, unsafe_allow_html=True)
                st.success("PDF报告生成成功！")

            except Exception as e:
                st.error(f"PDF生成失败: {e}")

if __name__ == "__main__":
    main()
