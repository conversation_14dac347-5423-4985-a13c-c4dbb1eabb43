# 中泰好买交易文件上传服务

根据中泰好买交易文件上传服务API文档实现的Python客户端，支持文件上传、下载和认证功能。

## 功能特性

- ✅ 自动登录获取access_token
- ✅ Token过期自动重新登录
- ✅ 单个文件上传
- ✅ 批量文件上传
- ✅ 成交文件下载
- ✅ 完善的错误处理和重试机制
- ✅ 详细的日志记录
- ✅ 指数退避重试策略

## 安装依赖

```bash
pip install requests
```

## 快速开始

### 基本使用

```python
from zt_haomai_uploader import ZTHaoMaiUploader

# 创建上传器实例（使用默认凭据）
uploader = ZTHaoMaiUploader()

# 上传单个文件
result = uploader.upload_file("strategy.txt")
if result["success"]:
    print("上传成功!")
else:
    print(f"上传失败: {result['error']}")
```

### 批量上传文件

```python
# 批量上传多个文件
file_paths = ["strategy1.txt", "strategy2.txt", "strategy3.txt"]
result = uploader.upload_files(file_paths)

if result["success"]:
    print(f"成功上传文件: {result['uploaded_files']}")
else:
    print(f"上传失败: {result['error']}")
```

### 下载成交文件

```python
# 下载成交文件
result = uploader.download_trade_files()

if result["success"]:
    # 保存文件内容
    with open("trade_files.dat", "wb") as f:
        f.write(result["content"])
    print("成交文件下载成功!")
else:
    print(f"下载失败: {result['error']}")
```

### 使用自定义凭据

```python
# 使用自定义用户名和密码
uploader = ZTHaoMaiUploader(
    username="your_username",
    password="your_password"
)
```

## API参考

### ZTHaoMaiUploader类

#### 构造函数

```python
ZTHaoMaiUploader(username="xtp_haomai", password="haomai_xtp_1")
```

**参数:**
- `username` (str): 用户名，默认为"xtp_haomai"
- `password` (str): 密码，默认为"haomai_xtp_1"

#### 方法

##### login()

手动登录获取access_token。

**返回值:**
- `bool`: 登录是否成功

##### upload_file(file_path, max_retries=3)

上传单个策略文件。

**参数:**
- `file_path` (str): 要上传的文件路径
- `max_retries` (int): 最大重试次数，默认为3

**返回值:**
- `dict`: 上传结果
  - `success` (bool): 是否成功
  - `uploaded_files` (list): 成功上传的文件列表
  - `response` (dict): 服务器响应
  - `error` (str): 错误信息（如果失败）

##### upload_files(file_paths, max_retries=3)

批量上传策略文件。

**参数:**
- `file_paths` (list): 要上传的文件路径列表
- `max_retries` (int): 最大重试次数，默认为3

**返回值:**
- `dict`: 上传结果（格式同upload_file）

##### download_trade_files(max_retries=3)

下载成交文件。

**参数:**
- `max_retries` (int): 最大重试次数，默认为3

**返回值:**
- `dict`: 下载结果
  - `success` (bool): 是否成功
  - `content` (bytes): 文件内容
  - `headers` (dict): 响应头
  - `response` (dict): JSON响应（如果适用）
  - `error` (str): 错误信息（如果失败）

## 错误处理

该库包含完善的错误处理机制：

1. **网络错误**: 自动重试，使用指数退避策略
2. **认证错误**: 自动重新登录
3. **文件错误**: 检查文件是否存在
4. **Token过期**: 自动刷新token

## 日志记录

库会自动记录详细的操作日志，包括：
- 登录状态
- 上传/下载进度
- 错误信息
- 重试信息

## 运行示例

```bash
# 运行完整示例
python zt_uploader_example.py

# 或者直接运行主文件查看基本示例
python zt_haomai_uploader.py
```

## 注意事项

1. **Token有效期**: access_token有效期为24小时，库会自动处理token过期和刷新
2. **文件大小**: 请注意上传文件的大小限制
3. **网络超时**: 默认超时时间为60秒，可根据需要调整
4. **并发限制**: 建议不要同时创建多个实例进行并发操作

## 故障排除

### 常见问题

1. **登录失败**
   - 检查用户名和密码是否正确
   - 确认网络连接正常
   - 检查API端点是否可访问

2. **文件上传失败**
   - 确认文件存在且可读
   - 检查文件大小是否超限
   - 确认token是否有效

3. **网络连接问题**
   - 检查防火墙设置
   - 确认代理配置（如果使用代理）
   - 验证DNS解析

### 调试模式

启用详细日志记录：

```python
import logging
logging.basicConfig(level=logging.DEBUG)

uploader = ZTHaoMaiUploader()
```

## 许可证

本项目仅供学习和内部使用。

## 更新日志

### v1.0.0
- 初始版本
- 支持文件上传、下载功能
- 完善的错误处理和重试机制
- 自动token管理
