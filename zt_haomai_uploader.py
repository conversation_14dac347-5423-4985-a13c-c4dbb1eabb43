"""
中泰好买交易文件上传服务
根据API文档实现的文件上传、下载和认证功能
"""

import requests
import os
import json
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import time


class ZTHaoMaiUploader:
    """中泰好买交易文件上传服务类"""
    
    def __init__(self, username: str = "xtp_haomai", password: str = "haomai_xtp_1"):
        """
        初始化上传服务
        
        Args:
            username: 用户名，默认为xtp_haomai
            password: 密码，默认为haomai_xtp_1
        """
        self.username = username
        self.password = password
        self.broker = "xtp_haomai"  # 固定值
        
        # API端点
        self.base_url = "https://horizon.superquant.fund"
        self.login_url = f"{self.base_url}/api/cas/user/login"
        self.upload_url = f"{self.base_url}/apps/trade-helper/api/trade/file/upload/strategy"
        self.download_url = f"{self.base_url}/apps/trade-helper/api/trade/file/trade-files"
        
        # 认证信息
        self.access_token: Optional[str] = None
        self.token_expires_at: Optional[datetime] = None
        
        # 配置日志
        self.logger = logging.getLogger(__name__)
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def _is_token_valid(self) -> bool:
        """检查token是否有效"""
        if not self.access_token or not self.token_expires_at:
            return False
        return datetime.now() < self.token_expires_at
    
    def login(self) -> bool:
        """
        登录获取access_token
        
        Returns:
            bool: 登录是否成功
        """
        try:
            self.logger.info("正在登录...")
            
            payload = {
                "username": self.username,
                "password": self.password
            }
            
            response = requests.post(
                self.login_url,
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                self.access_token = data.get("access_token")
                if self.access_token:
                    # token有效期24小时，提前1小时过期以确保安全
                    self.token_expires_at = datetime.now() + timedelta(hours=23)
                    self.logger.info("登录成功")
                    return True
                else:
                    self.logger.error("登录响应中未找到access_token")
                    return False
            else:
                self.logger.error(f"登录失败，状态码: {response.status_code}, 响应: {response.text}")
                return False
                
        except Exception as e:
            self.logger.error(f"登录过程中发生异常: {str(e)}")
            return False
    
    def _ensure_authenticated(self) -> bool:
        """确保已认证，如果token无效则重新登录"""
        if self._is_token_valid():
            return True
        
        self.logger.info("Token无效或已过期，重新登录...")
        return self.login()
    
    def _get_auth_headers(self) -> Dict[str, str]:
        """获取认证头"""
        return {
            "Authorization": f"Bearer {self.access_token}"
        }
    
    def upload_files(self, file_paths: List[str], max_retries: int = 3) -> Dict[str, Any]:
        """
        上传策略文件
        
        Args:
            file_paths: 要上传的文件路径列表
            max_retries: 最大重试次数
            
        Returns:
            Dict: 上传结果
        """
        if not file_paths:
            return {"success": False, "error": "文件路径列表为空"}
        
        # 检查文件是否存在
        missing_files = []
        for file_path in file_paths:
            if not os.path.exists(file_path):
                missing_files.append(file_path)
        
        if missing_files:
            return {
                "success": False, 
                "error": f"以下文件不存在: {', '.join(missing_files)}"
            }
        
        for attempt in range(max_retries + 1):
            try:
                # 确保已认证
                if not self._ensure_authenticated():
                    return {"success": False, "error": "认证失败"}
                
                self.logger.info(f"开始上传文件 (尝试 {attempt + 1}/{max_retries + 1}): {file_paths}")
                
                # 准备文件数据
                files = []
                for file_path in file_paths:
                    files.append(('files', (os.path.basename(file_path), open(file_path, 'rb'))))
                
                # 准备表单数据
                data = {'broker': self.broker}
                
                try:
                    response = requests.post(
                        self.upload_url,
                        files=files,
                        data=data,
                        headers=self._get_auth_headers(),
                        timeout=60
                    )
                    
                    if response.status_code == 200:
                        self.logger.info("文件上传成功")
                        return {
                            "success": True,
                            "response": response.json() if response.content else {},
                            "uploaded_files": [os.path.basename(f) for f in file_paths]
                        }
                    elif response.status_code == 401:
                        self.logger.warning("认证失败，token可能已过期")
                        self.access_token = None  # 清除无效token
                        if attempt < max_retries:
                            continue  # 重试
                    else:
                        error_msg = f"上传失败，状态码: {response.status_code}, 响应: {response.text}"
                        self.logger.error(error_msg)
                        if attempt == max_retries:
                            return {"success": False, "error": error_msg}
                
                finally:
                    # 关闭文件
                    for _, file_tuple in files:
                        if hasattr(file_tuple[1], 'close'):
                            file_tuple[1].close()
                
            except Exception as e:
                error_msg = f"上传过程中发生异常: {str(e)}"
                self.logger.error(error_msg)
                if attempt == max_retries:
                    return {"success": False, "error": error_msg}
            
            # 重试前等待
            if attempt < max_retries:
                wait_time = 2 ** attempt  # 指数退避
                self.logger.info(f"等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
        
        return {"success": False, "error": "达到最大重试次数"}
    
    def upload_file(self, file_path: str, max_retries: int = 3) -> Dict[str, Any]:
        """
        上传单个策略文件
        
        Args:
            file_path: 要上传的文件路径
            max_retries: 最大重试次数
            
        Returns:
            Dict: 上传结果
        """
        return self.upload_files([file_path], max_retries)
    
    def download_trade_files(self, max_retries: int = 3) -> Dict[str, Any]:
        """
        下载成交文件
        
        Args:
            max_retries: 最大重试次数
            
        Returns:
            Dict: 下载结果
        """
        for attempt in range(max_retries + 1):
            try:
                # 确保已认证
                if not self._ensure_authenticated():
                    return {"success": False, "error": "认证失败"}
                
                self.logger.info(f"开始下载成交文件 (尝试 {attempt + 1}/{max_retries + 1})")
                
                params = {'broker': self.broker}
                
                response = requests.get(
                    self.download_url,
                    params=params,
                    headers=self._get_auth_headers(),
                    timeout=60
                )
                
                if response.status_code == 200:
                    self.logger.info("成交文件下载成功")
                    return {
                        "success": True,
                        "content": response.content,
                        "headers": dict(response.headers),
                        "response": response.json() if response.content and 
                                  response.headers.get('content-type', '').startswith('application/json') 
                                  else None
                    }
                elif response.status_code == 401:
                    self.logger.warning("认证失败，token可能已过期")
                    self.access_token = None  # 清除无效token
                    if attempt < max_retries:
                        continue  # 重试
                else:
                    error_msg = f"下载失败，状态码: {response.status_code}, 响应: {response.text}"
                    self.logger.error(error_msg)
                    if attempt == max_retries:
                        return {"success": False, "error": error_msg}
                        
            except Exception as e:
                error_msg = f"下载过程中发生异常: {str(e)}"
                self.logger.error(error_msg)
                if attempt == max_retries:
                    return {"success": False, "error": error_msg}
            
            # 重试前等待
            if attempt < max_retries:
                wait_time = 2 ** attempt  # 指数退避
                self.logger.info(f"等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
        
        return {"success": False, "error": "达到最大重试次数"}


def main():
    """使用示例"""
    # 创建上传服务实例
    uploader = ZTHaoMaiUploader()
    
    # 示例1: 上传单个文件
    file_path = "strategy.txt"  # 替换为实际文件路径
    if os.path.exists(file_path):
        result = uploader.upload_file(file_path)
        print(f"上传结果: {result}")
    
    # 示例2: 上传多个文件
    file_paths = ["strategy1.txt", "strategy2.txt"]  # 替换为实际文件路径
    existing_files = [f for f in file_paths if os.path.exists(f)]
    if existing_files:
        result = uploader.upload_files(existing_files)
        print(f"批量上传结果: {result}")
    
    # 示例3: 下载成交文件
    result = uploader.download_trade_files()
    print(f"下载结果: {result}")


if __name__ == "__main__":
    main()
