import os, sys
from idna import encode
import pandas as pd
current_dir = os.path.dirname(os.path.abspath(__file__))

# 将项目路径添加到模块搜索路径
project_dir = os.path.abspath(os.path.join(current_dir, ".."))  # 通过..返回上一级目录
sys.path.append(project_dir)


from loguru import logger

import datetime
from misc.Readstockfile import read_remote_file, write_file
from misc.ssh_conn import sftp_clent_wintrader, ftp_clent_zx_zhongtai


def copy_files(date):
    
    src_dir = f'数据导出/中泰SmartX/{date}/'
    target_dir = f'/home/<USER>/dav/accounts/超量子中泰/客户端导出/{date}/'
    if not os.path.exists(target_dir):
        os.makedirs(target_dir)
    
    files = [
        # f'basket_{date}.csv',
        # f'parentorder_{date}.csv',
        # f'order_{date}.csv',
        # f'transaction_{date}.csv',
        # f't0_basket_{date}.csv',
        # f't0_parentorder_{date}.csv',
        # f't0_order_{date}.csv',
        # f't0_transaction_{date}.csv',
    ]
    all_dir_files = sftp_clent_wintrader.listdir(src_dir)
    for file in all_dir_files:
        if file.startswith(f'母单列表_{date}') or file.startswith(f'basket_{date}'):
            files.append(file)
        if file.startswith(f'母单标的_{date}') or file.startswith(f'parentorder_{date}'):
            files.append(file)
        if file.startswith(f'委托列表_{date}') or file.startswith(f'order_{date}'):
            files.append(file)
        if file.startswith(f'成交列表_{date}') or file.startswith(f'transaction_{date}'):
            files.append(file)
        if file.startswith(f't0_母单列表_{date}') or file.startswith(f't0_basket_{date}'):
            files.append(file)
        if file.startswith(f't0_母单标的_{date}') or file.startswith(f't0_parentorder_{date}'):
            files.append(file)
        if file.startswith(f't0_委托列表_{date}') or file.startswith(f't0_order_{date}'):
            files.append(file)
        if file.startswith(f't0_成交列表_{date}') or file.startswith(f't0_transaction_{date}'):
            files.append(file)
    
    for file in files:
        sftp_clent_wintrader.get(os.path.join(src_dir, file), os.path.join(target_dir, file))
        
    all_dir_files_2 = os.listdir(target_dir)
    for file in all_dir_files_2:
        if file.startswith(f'母单列表_{date}'):
            os.rename(os.path.join(target_dir, file), os.path.join(target_dir, f'basket_{date}.csv'))
        if file.startswith(f'母单标的_{date}'):
            os.rename(os.path.join(target_dir, file), os.path.join(target_dir, f'parentorder_{date}.csv'))
        if file.startswith(f'委托列表_{date}'):
            os.rename(os.path.join(target_dir, file), os.path.join(target_dir, f'order_{date}.csv'))
        if file.startswith(f'成交列表_{date}'):
            os.rename(os.path.join(target_dir, file), os.path.join(target_dir, f'transaction_{date}.csv'))
        if file.startswith(f't0_母单列表_{date}'):
            os.rename(os.path.join(target_dir, file), os.path.join(target_dir, f't0_basket_{date}.csv'))
        if file.startswith(f't0_母单标的_{date}'):
            os.rename(os.path.join(target_dir, file), os.path.join(target_dir, f't0_parentorder_{date}.csv'))
        if file.startswith(f't0_委托列表_{date}'):
            os.rename(os.path.join(target_dir, file), os.path.join(target_dir, f't0_order_{date}.csv'))
        if file.startswith(f't0_成交列表_{date}'):
            os.rename(os.path.join(target_dir, file), os.path.join(target_dir, f't0_transaction_{date}.csv'))
    

        
    src_dir = f'数据导出/中泰SmartX/自动导出/{date}/'
    target_dir = f'/home/<USER>/dav/accounts/超量子中泰/客户端导出/{date}/'
    if not os.path.exists(target_dir):
        os.makedirs(target_dir)
    
    files = [
        'xtp_************_algoList.csv',
        'xtp_************_algoTickerList.csv',
        'xtp_************_Asset.csv',
        'xtp_************_Order.csv',
        'xtp_************_Position.csv',
        'xtp_************_Trade.csv',
    ]
    for file in files:
        sftp_clent_wintrader.get(os.path.join(src_dir, file), os.path.join(target_dir, file))


def filter_tuoguan_t0(date):
    file = 'xtp_************_Trade.csv'
    target_file = f't0_transaction_2_{date}.csv'
    target_dir = f'/home/<USER>/dav/accounts/超量子中泰/客户端导出/{date}/'
    
    df = pd.read_csv(os.path.join(target_dir, file))
    df = df[df['渠道'].isna()]
    print(df.head())
    print(df.shape)
    
    df.to_csv(os.path.join(target_dir, target_file), index=False, encoding='gbk')
    
def upload_files(date):
    src_dir = f'/home/<USER>/dav/accounts/超量子中泰/客户端导出/{date}/'
    target_dir = f'chaolz_zz1000/daily_after/'
    if date not in ftp_clent_zx_zhongtai.listdir(target_dir):
        ftp_clent_zx_zhongtai.mkdir(os.path.join(target_dir, date))
    
    files = [
        f'basket_{date}.csv',
        f'parentorder_{date}.csv',
        f'order_{date}.csv',
        f'transaction_{date}.csv',
        f't0_basket_{date}.csv',
        f't0_parentorder_{date}.csv',
        f't0_order_{date}.csv',
        f't0_transaction_{date}.csv',
        # f't0_transaction_2_{date}.csv',
        'xtp_************_algoList.csv',
        'xtp_************_algoTickerList.csv',
        'xtp_************_Asset.csv',
        'xtp_************_Order.csv',
        'xtp_************_Position.csv',
        'xtp_************_Trade.csv',
    ]
    for file in files:
        ftp_clent_zx_zhongtai.put(os.path.join(src_dir, file), os.path.join(target_dir, date, file))
    

def record_account_info(date):
    src_dir = f'/home/<USER>/dav/accounts/超量子中泰/客户端导出/{date}/'
    target_dir = f'/home/<USER>/dav/accounts/超量子中泰/account/'
    src_file = f'xtp_************_Asset.csv'
    df = pd.read_csv(os.path.join(src_dir, src_file), dtype={'资金账号': str})
    account_id = '************'
    # trim the space
    df = df.map(lambda x: x.strip() if isinstance(x, str) else x)
    for col in df.columns:
        if col != '资金账号':
            df[col] = df[col].astype(float)
    
    df.set_index('资金账号', inplace=True)
    df2 = pd.DataFrame()
    df2.loc[0, 'date']    = date
    df2.loc[0, 'net_asset']      = df.loc[account_id, '净资产']
    df2.loc[0, 'available_fund'] = df.loc[account_id, '可用资金']
    df2.loc[0, 'stock_value']    = df.loc[account_id, '总市值'] - df.loc[account_id, '回购总市值']
    df2.to_csv(os.path.join(target_dir, f'accountinfo_{date}.csv'), index=False, encoding='gbk')
    
if __name__ == '__main__':
    if len(sys.argv) > 1:
        date = str(sys.argv[1])
    else:
        date = datetime.datetime.now().strftime('%Y%m%d')
    
    copy_files(date)
    # filter_tuoguan_t0(date)
    upload_files(date)
    record_account_info(date)