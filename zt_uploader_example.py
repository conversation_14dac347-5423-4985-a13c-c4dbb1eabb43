"""
中泰好买交易文件上传服务使用示例
演示如何使用ZTHaoMaiUploader类进行文件上传和下载
"""

import os
import json
from zt_haomai_uploader import ZTHaoMaiUploader


def create_sample_files():
    """创建示例文件用于测试"""
    # 创建示例策略文件
    sample_content = """# 示例策略文件
策略名称: 测试策略
创建时间: 2024-01-01
策略描述: 这是一个用于测试上传功能的示例策略文件
"""
    
    with open("sample_strategy.txt", "w", encoding="utf-8") as f:
        f.write(sample_content)
    
    # 创建另一个示例文件
    sample_content2 = """# 示例策略文件2
策略名称: 测试策略2
创建时间: 2024-01-02
策略描述: 这是第二个用于测试批量上传功能的示例策略文件
"""
    
    with open("sample_strategy2.txt", "w", encoding="utf-8") as f:
        f.write(sample_content2)
    
    print("已创建示例文件: sample_strategy.txt, sample_strategy2.txt")


def example_single_file_upload():
    """示例1: 上传单个文件"""
    print("\n=== 示例1: 上传单个文件 ===")
    
    # 创建上传服务实例
    uploader = ZTHaoMaiUploader()
    
    # 上传单个文件
    file_path = "sample_strategy.txt"
    
    if not os.path.exists(file_path):
        print(f"文件 {file_path} 不存在，请先运行 create_sample_files()")
        return
    
    print(f"正在上传文件: {file_path}")
    result = uploader.upload_file(file_path)
    
    if result["success"]:
        print("✅ 文件上传成功!")
        print(f"上传的文件: {result.get('uploaded_files', [])}")
        if result.get("response"):
            print(f"服务器响应: {json.dumps(result['response'], indent=2, ensure_ascii=False)}")
    else:
        print("❌ 文件上传失败!")
        print(f"错误信息: {result.get('error', '未知错误')}")


def example_multiple_files_upload():
    """示例2: 批量上传多个文件"""
    print("\n=== 示例2: 批量上传多个文件 ===")
    
    # 创建上传服务实例
    uploader = ZTHaoMaiUploader()
    
    # 准备要上传的文件列表
    file_paths = ["sample_strategy.txt", "sample_strategy2.txt"]
    
    # 检查文件是否存在
    existing_files = [f for f in file_paths if os.path.exists(f)]
    missing_files = [f for f in file_paths if not os.path.exists(f)]
    
    if missing_files:
        print(f"以下文件不存在: {missing_files}")
        print("请先运行 create_sample_files()")
        if not existing_files:
            return
    
    print(f"正在批量上传文件: {existing_files}")
    result = uploader.upload_files(existing_files)
    
    if result["success"]:
        print("✅ 文件批量上传成功!")
        print(f"上传的文件: {result.get('uploaded_files', [])}")
        if result.get("response"):
            print(f"服务器响应: {json.dumps(result['response'], indent=2, ensure_ascii=False)}")
    else:
        print("❌ 文件批量上传失败!")
        print(f"错误信息: {result.get('error', '未知错误')}")


def example_download_trade_files():
    """示例3: 下载成交文件"""
    print("\n=== 示例3: 下载成交文件 ===")
    
    # 创建上传服务实例
    uploader = ZTHaoMaiUploader()
    
    print("正在下载成交文件...")
    result = uploader.download_trade_files()
    
    if result["success"]:
        print("✅ 成交文件下载成功!")
        
        # 如果返回的是JSON数据
        if result.get("response"):
            print(f"响应数据: {json.dumps(result['response'], indent=2, ensure_ascii=False)}")
        
        # 如果返回的是文件内容
        if result.get("content"):
            content_length = len(result["content"])
            print(f"下载内容大小: {content_length} 字节")
            
            # 根据响应头判断文件类型并保存
            headers = result.get("headers", {})
            content_type = headers.get("content-type", "")
            content_disposition = headers.get("content-disposition", "")
            
            # 尝试从content-disposition中提取文件名
            filename = "trade_files"
            if "filename=" in content_disposition:
                filename = content_disposition.split("filename=")[1].strip('"')
            elif content_type.startswith("application/json"):
                filename += ".json"
            elif content_type.startswith("text/"):
                filename += ".txt"
            else:
                filename += ".dat"
            
            # 保存文件
            with open(filename, "wb") as f:
                f.write(result["content"])
            print(f"文件已保存为: {filename}")
    else:
        print("❌ 成交文件下载失败!")
        print(f"错误信息: {result.get('error', '未知错误')}")


def example_custom_credentials():
    """示例4: 使用自定义凭据"""
    print("\n=== 示例4: 使用自定义凭据 ===")
    
    # 使用自定义用户名和密码创建实例
    custom_uploader = ZTHaoMaiUploader(
        username="your_username",  # 替换为实际用户名
        password="your_password"   # 替换为实际密码
    )
    
    # 手动登录测试
    print("正在测试登录...")
    login_success = custom_uploader.login()
    
    if login_success:
        print("✅ 登录成功!")
        print(f"Token有效期至: {custom_uploader.token_expires_at}")
    else:
        print("❌ 登录失败!")
        print("请检查用户名和密码是否正确")


def example_error_handling():
    """示例5: 错误处理演示"""
    print("\n=== 示例5: 错误处理演示 ===")
    
    uploader = ZTHaoMaiUploader()
    
    # 尝试上传不存在的文件
    print("1. 尝试上传不存在的文件...")
    result = uploader.upload_file("nonexistent_file.txt")
    print(f"结果: {result}")
    
    # 尝试上传空文件列表
    print("\n2. 尝试上传空文件列表...")
    result = uploader.upload_files([])
    print(f"结果: {result}")


def simple_upload_example(file_path: str):
    """
    简单上传示例 - 最基本的使用方式
    
    Args:
        file_path: 要上传的文件路径
    """
    print(f"\n=== 简单上传示例: {file_path} ===")
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    # 创建上传器并上传文件
    uploader = ZTHaoMaiUploader()
    result = uploader.upload_file(file_path)
    
    # 处理结果
    if result["success"]:
        print(f"✅ 文件 {file_path} 上传成功!")
        return True
    else:
        print(f"❌ 文件 {file_path} 上传失败: {result.get('error', '未知错误')}")
        return False


def main():
    """主函数 - 运行所有示例"""
    print("中泰好买交易文件上传服务使用示例")
    print("=" * 50)
    
    # 创建示例文件
    create_sample_files()
    
    # 运行各种示例
    try:
        example_single_file_upload()
        # example_multiple_files_upload()
        # example_download_trade_files()
        # example_custom_credentials()
        # example_error_handling()
        
        # 简单使用示例
        simple_upload_example("sample_strategy.txt")
        
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"\n运行示例时发生错误: {str(e)}")
    
    print("\n示例运行完成!")


if __name__ == "__main__":
    main()
